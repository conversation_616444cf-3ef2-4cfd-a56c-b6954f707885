# Testing Image Analysis Service

This guide explains how to test the ImageAnalysisService before integrating it into your camera flow.

## 🧪 Testing Options

### 1. **Unit Tests** (Recommended First Step)
Run the automated unit tests to verify basic functionality:

```bash
npm test src/__tests__/services/ImageAnalysisService.test.js
```

These tests verify:
- Service initialization
- Image format handling
- Blur detection logic
- Error handling
- Threshold interpretation

### 2. **Test Screen Integration**
Use the built-in TestScreen for manual testing:

1. Navigate to your TestScreen in the app
2. Scroll to the "Image Analysis Test" section
3. Enter a full path to a test image (e.g., `/path/to/your/test/image.jpg`)
4. Tap "Test Blur Detection"
5. Review the results showing variance, interpretation, and timing

### 3. **Programmatic Testing**
Use the test utilities for more comprehensive testing:

```javascript
import ImageAnalysisTestUtils from '~/utils/ImageAnalysisTestUtils';

// Quick test of a single image
const result = await ImageAnalysisTestUtils.quickTest('/path/to/image.jpg');

// Test multiple images
const batchResult = await ImageAnalysisTestUtils.batchTest([
  '/path/to/sharp/image1.jpg',
  '/path/to/blurry/image1.jpg',
  '/path/to/sharp/image2.jpg',
]);

// Performance testing
const perfResult = await ImageAnalysisTestUtils.performanceTest('/path/to/image.jpg', 10);

// Camera integration test
const cameraResult = await ImageAnalysisTestUtils.testCameraIntegration('/path/from/camera.jpg');
```

## 📱 Getting Test Images

### Option 1: Use Your Camera
1. Take several photos with your device camera:
   - 2-3 sharp, well-focused photos
   - 2-3 intentionally blurry photos (shake camera while taking)
   - 1-2 slightly blurry photos
2. Note the file paths (usually in `/data/data/your.app.id/cache/` or similar)

### Option 2: Use Sample Images
1. Download test images from the internet
2. Add them to your device/simulator
3. Use their file paths for testing

### Option 3: Camera Integration Test
1. Use your existing CameraViewScreen to take a photo
2. Note the `photoPath` from the camera result
3. Test with that path using the TestScreen or utilities

## 🎯 What to Look For

### **Variance Values**
- **< 50**: Very Blurry (should be flagged for retake)
- **50-100**: Blurry (should be flagged for retake)  
- **100-200**: Slightly Blurry (may be acceptable depending on use case)
- **> 200**: Sharp (good quality)

### **Performance**
- Analysis should complete in **< 1 second** for typical camera photos
- Memory usage should remain stable (no leaks)

### **Accuracy**
- Sharp images should be correctly identified as sharp
- Obviously blurry images should be flagged as blurry
- Edge cases (slightly blurry) may need threshold adjustment

## 🔧 Threshold Tuning

If the default threshold (100) doesn't work well for your images:

```javascript
// Test different thresholds
const result50 = await ImageAnalysisService.isImageBlurry(imagePath, 'path', 50);
const result100 = await ImageAnalysisService.isImageBlurry(imagePath, 'path', 100);
const result150 = await ImageAnalysisService.isImageBlurry(imagePath, 'path', 150);

console.log('Threshold 50:', result50);
console.log('Threshold 100:', result100);
console.log('Threshold 150:', result150);
```

Use the threshold validation utility:

```javascript
const testCases = [
  { path: '/path/to/sharp1.jpg', expectedBlurry: false, description: 'Sharp photo 1' },
  { path: '/path/to/blurry1.jpg', expectedBlurry: true, description: 'Blurry photo 1' },
  // Add more test cases...
];

const validation = await ImageAnalysisTestUtils.validateThresholds(testCases);
console.log(`Accuracy: ${validation.accuracy}%`);
```

## 🚀 Integration with Camera Flow

Once testing is complete, integrate with your camera:

```javascript
// In CameraViewScreen.tsx, after taking a photo:
const handleSubmitPhoto = async () => {
  if (photoPath) {
    // Analyze the photo before submitting
    const blurResult = await ImageAnalysisService.analyzeBlur(photoPath, 'path');
    
    if (blurResult?.isBlurry) {
      Alert.alert(
        'Blurry Photo Detected',
        'This photo appears to be blurry. Would you like to retake it?',
        [
          { text: 'Retake', onPress: () => setPhotoPath(null) },
          { text: 'Use Anyway', onPress: () => proceedWithPhoto() },
        ]
      );
    } else {
      proceedWithPhoto();
    }
  }
};
```

## 🐛 Troubleshooting

### **"Analysis returned null"**
- Check that the image path is correct and accessible
- Verify the image format is supported (JPG, PNG)
- Check console for detailed error messages

### **"OpenCV initialization failed"**
- Ensure `@techstark/opencv-js` is properly installed
- Check that the app has necessary permissions
- Try restarting the app

### **Poor accuracy**
- Test with more diverse image samples
- Adjust the blur threshold based on your specific use case
- Consider image size - very large images may need resizing

### **Slow performance**
- Test with typical camera photo sizes
- Consider resizing very large images before analysis
- Check device performance and memory usage

## 📊 Expected Results

For a well-functioning service, you should see:
- **Accuracy**: 80-90% correct blur detection on obvious cases
- **Performance**: < 500ms analysis time for typical photos
- **Reliability**: Consistent results across multiple runs
- **Memory**: No memory leaks during repeated analysis

## 🔄 Next Steps

1. Run unit tests to verify basic functionality
2. Use TestScreen to manually test with real images
3. Tune threshold if needed based on your image types
4. Run performance tests to ensure acceptable speed
5. Integrate into camera flow with user-friendly prompts

Once testing is complete and you're satisfied with the results, you can confidently integrate the service into your camera capture workflow!
