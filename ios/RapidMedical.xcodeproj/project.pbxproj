// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* RapidMedicalTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* RapidMedicalTests.m */; };
		0C94CC8A2E0C35B5001B0BA4 /* OneSignalNotificationServiceExtensionDev.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 0C94CC792E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		10C7CA3A5226D2BCFEF0D936 /* libPods-RapidMedicalDev.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C181DE32001BBF76053CBE2F /* libPods-RapidMedicalDev.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		3498E7792DDCAA4C001585F0 /* MapboxNavigationViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3498E7782DDCAA4C001585F0 /* MapboxNavigationViewManager.swift */; };
		3498E77A2DDCAA4C001585F0 /* MapboxNavigationViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3498E7782DDCAA4C001585F0 /* MapboxNavigationViewManager.swift */; };
		3498E77B2DDCAA4C001585F0 /* MapboxNavigationViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3498E7782DDCAA4C001585F0 /* MapboxNavigationViewManager.swift */; };
		3498E77D2DDCAEA9001585F0 /* MapboxNavigationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3498E77C2DDCAEA9001585F0 /* MapboxNavigationView.swift */; };
		3498E77E2DDCAEA9001585F0 /* MapboxNavigationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3498E77C2DDCAEA9001585F0 /* MapboxNavigationView.swift */; };
		3498E77F2DDCAEA9001585F0 /* MapboxNavigationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3498E77C2DDCAEA9001585F0 /* MapboxNavigationView.swift */; };
		3498E7812DDCB11F001585F0 /* MapboxNavigationViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 3498E7802DDCB117001585F0 /* MapboxNavigationViewManager.m */; };
		3498E7822DDCB11F001585F0 /* MapboxNavigationViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 3498E7802DDCB117001585F0 /* MapboxNavigationViewManager.m */; };
		3498E7832DDCB11F001585F0 /* MapboxNavigationViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 3498E7802DDCB117001585F0 /* MapboxNavigationViewManager.m */; };
		53B7B354A245AB4BBC5090BD /* libPods-RapidMedical.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AEAFEF95993B5F1743B40DEB /* libPods-RapidMedical.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		8364BE162D5CA8AE00DF7287 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 8364BE152D5CA8AE00DF7287 /* assets */; };
		83E213512D5CA32600368ACD /* main.jsbundle in Resources */ = {isa = PBXBuildFile; fileRef = 83E213502D5CA32600368ACD /* main.jsbundle */; };
		A07350928CD1F18E4CA9AF66 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = AAD630199D00F157475A8F38 /* PrivacyInfo.xcprivacy */; };
		AA04FB562D30F3BE00A7AC30 /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		AA2AF29D2DEF718B004FF500 /* OneSignalNotificationServiceExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = AA2AF2962DEF718B004FF500 /* OneSignalNotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		AA8F89E12D2CD9D700A298E8 /* Satoshi-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D92D2CD9D700A298E8 /* Satoshi-Light.ttf */; };
		AA8F89E22D2CD9D700A298E8 /* Satoshi-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D62D2CD9D700A298E8 /* Satoshi-Bold.ttf */; };
		AA8F89E42D2CD9D700A298E8 /* Satoshi-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DD2D2CD9D700A298E8 /* Satoshi-Regular.ttf */; };
		AA8F89E52D2CD9D700A298E8 /* Satoshi-Variable.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DE2D2CD9D700A298E8 /* Satoshi-Variable.ttf */; };
		AA8F89E62D2CD9D700A298E8 /* Satoshi-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D42D2CD9D700A298E8 /* Satoshi-Black.ttf */; };
		AA8F89E82D2CD9D700A298E8 /* Satoshi-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DB2D2CD9D700A298E8 /* Satoshi-Medium.ttf */; };
		AA8F89F92D2CD9D700A298E8 /* Satoshi-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D92D2CD9D700A298E8 /* Satoshi-Light.ttf */; };
		AA8F89FA2D2CD9D700A298E8 /* Satoshi-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D62D2CD9D700A298E8 /* Satoshi-Bold.ttf */; };
		AA8F89FC2D2CD9D700A298E8 /* Satoshi-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DD2D2CD9D700A298E8 /* Satoshi-Regular.ttf */; };
		AA8F89FD2D2CD9D700A298E8 /* Satoshi-Variable.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DE2D2CD9D700A298E8 /* Satoshi-Variable.ttf */; };
		AA8F89FE2D2CD9D700A298E8 /* Satoshi-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D42D2CD9D700A298E8 /* Satoshi-Black.ttf */; };
		AA8F8A002D2CD9D700A298E8 /* Satoshi-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DB2D2CD9D700A298E8 /* Satoshi-Medium.ttf */; };
		AAA344C82DAFC4520028FDB0 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		AAA344C92DAFC4520028FDB0 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		AAA344CD2DAFC4520028FDB0 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 8364BE152D5CA8AE00DF7287 /* assets */; };
		AAA344CE2DAFC4520028FDB0 /* main.jsbundle in Resources */ = {isa = PBXBuildFile; fileRef = 83E213502D5CA32600368ACD /* main.jsbundle */; };
		AAA344CF2DAFC4520028FDB0 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		AAA344D02DAFC4520028FDB0 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		AAA344D12DAFC4520028FDB0 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = AAD630199D00F157475A8F38 /* PrivacyInfo.xcprivacy */; };
		AAA344D22DAFC4520028FDB0 /* Satoshi-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D92D2CD9D700A298E8 /* Satoshi-Light.ttf */; };
		AAA344D42DAFC4520028FDB0 /* Satoshi-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D62D2CD9D700A298E8 /* Satoshi-Bold.ttf */; };
		AAA344D52DAFC4520028FDB0 /* Satoshi-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DD2D2CD9D700A298E8 /* Satoshi-Regular.ttf */; };
		AAA344D62DAFC4520028FDB0 /* Satoshi-Variable.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DE2D2CD9D700A298E8 /* Satoshi-Variable.ttf */; };
		AAA344D72DAFC4520028FDB0 /* Satoshi-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89D42D2CD9D700A298E8 /* Satoshi-Black.ttf */; };
		AAA344D82DAFC4520028FDB0 /* Satoshi-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AA8F89DB2D2CD9D700A298E8 /* Satoshi-Medium.ttf */; };
		AACA52102C8E7A360019BF60 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		AACA52112C8E7A360019BF60 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		AACA52152C8E7A360019BF60 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		AACA52162C8E7A360019BF60 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		AACA52172C8E7A360019BF60 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = AAD630199D00F157475A8F38 /* PrivacyInfo.xcprivacy */; };
		C7D797EF5AA777ACCFCD464A /* libPods-OneSignalNotificationServiceExtensionDev.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F01012CA16A80FE8CE4F0563 /* libPods-OneSignalNotificationServiceExtensionDev.a */; };
		CDB47483DFBC9D3FDF9C4386 /* libPods-OneSignalNotificationServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 70C3A3B4D157029611C0764D /* libPods-OneSignalNotificationServiceExtension.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = RapidMedical;
		};
		0C94CC8B2E0C35B5001B0BA4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0C94CC702E0C320A001B0BA4;
			remoteInfo = OneSignalNotificationServiceExtensionDev;
		};
		AA2AF29B2DEF718B004FF500 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AA2AF2952DEF718B004FF500;
			remoteInfo = OneSignalNotificationServiceExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		0C94CC8D2E0C35B5001B0BA4 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				0C94CC8A2E0C35B5001B0BA4 /* OneSignalNotificationServiceExtensionDev.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		AA2AF2A22DEF718B004FF500 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				AA2AF29D2DEF718B004FF500 /* OneSignalNotificationServiceExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* RapidMedicalTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RapidMedicalTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* RapidMedicalTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RapidMedicalTests.m; sourceTree = "<group>"; };
		0C94CC792E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = OneSignalNotificationServiceExtensionDev.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07F961A680F5B00A75B9A /* RapidMedical.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RapidMedical.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = RapidMedical/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = RapidMedical/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = RapidMedical/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = RapidMedical/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = RapidMedical/main.m; sourceTree = "<group>"; };
		1EA4BBE01BD76005B0310D9C /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtension/Pods-OneSignalNotificationServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		338D505FB23D241BE6542426 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtension/Pods-OneSignalNotificationServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		3498E7782DDCAA4C001585F0 /* MapboxNavigationViewManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapboxNavigationViewManager.swift; sourceTree = "<group>"; };
		3498E77C2DDCAEA9001585F0 /* MapboxNavigationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapboxNavigationView.swift; sourceTree = "<group>"; };
		3498E7802DDCB117001585F0 /* MapboxNavigationViewManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MapboxNavigationViewManager.m; sourceTree = "<group>"; };
		3498E7892DDCB235001585F0 /* MapboxNavigation-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MapboxNavigation-Bridging-Header.h"; sourceTree = "<group>"; };
		35274856859E117550E0248A /* Pods-RapidMedicalDev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RapidMedicalDev.debug.xcconfig"; path = "Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev.debug.xcconfig"; sourceTree = "<group>"; };
		35DB149155C63963110D1A30 /* Pods-RapidMedical.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RapidMedical.release.xcconfig"; path = "Target Support Files/Pods-RapidMedical/Pods-RapidMedical.release.xcconfig"; sourceTree = "<group>"; };
		70C3A3B4D157029611C0764D /* libPods-OneSignalNotificationServiceExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-OneSignalNotificationServiceExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		7406721B93EA195E559A50A9 /* Pods-RapidMedicalDev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RapidMedicalDev.release.xcconfig"; path = "Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev.release.xcconfig"; sourceTree = "<group>"; };
		78463E70F5CC22651BA37995 /* Pods-OneSignalNotificationServiceExtensionDev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtensionDev.debug.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtensionDev/Pods-OneSignalNotificationServiceExtensionDev.debug.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = RapidMedical/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8364BE152D5CA8AE00DF7287 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = "<group>"; };
		83E213502D5CA32600368ACD /* main.jsbundle */ = {isa = PBXFileReference; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		A766C19011B0C52469356D5E /* Pods-OneSignalNotificationServiceExtensionDev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtensionDev.release.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtensionDev/Pods-OneSignalNotificationServiceExtensionDev.release.xcconfig"; sourceTree = "<group>"; };
		AA2AF28F2DEF7020004FF500 /* RapidMedical.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = RapidMedical.entitlements; path = RapidMedical/RapidMedical.entitlements; sourceTree = "<group>"; };
		AA2AF2902DEF7058004FF500 /* RapidMedicalDev.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RapidMedicalDev.entitlements; sourceTree = "<group>"; };
		AA2AF2912DEF705E004FF500 /* RapidMedicalStaging.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RapidMedicalStaging.entitlements; sourceTree = "<group>"; };
		AA2AF2962DEF718B004FF500 /* OneSignalNotificationServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = OneSignalNotificationServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		AA7FBABC2DAFF02F00588FD4 /* hermes.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = hermes.xcframework; path = "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework"; sourceTree = "<group>"; };
		AA8F89D42D2CD9D700A298E8 /* Satoshi-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Black.ttf"; sourceTree = "<group>"; };
		AA8F89D52D2CD9D700A298E8 /* Satoshi-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-BlackItalic.ttf"; sourceTree = "<group>"; };
		AA8F89D62D2CD9D700A298E8 /* Satoshi-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Bold.ttf"; sourceTree = "<group>"; };
		AA8F89D72D2CD9D700A298E8 /* Satoshi-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-BoldItalic.ttf"; sourceTree = "<group>"; };
		AA8F89D82D2CD9D700A298E8 /* Satoshi-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Italic.ttf"; sourceTree = "<group>"; };
		AA8F89D92D2CD9D700A298E8 /* Satoshi-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Light.ttf"; sourceTree = "<group>"; };
		AA8F89DA2D2CD9D700A298E8 /* Satoshi-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-LightItalic.ttf"; sourceTree = "<group>"; };
		AA8F89DB2D2CD9D700A298E8 /* Satoshi-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Medium.ttf"; sourceTree = "<group>"; };
		AA8F89DC2D2CD9D700A298E8 /* Satoshi-MediumItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-MediumItalic.ttf"; sourceTree = "<group>"; };
		AA8F89DD2D2CD9D700A298E8 /* Satoshi-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Regular.ttf"; sourceTree = "<group>"; };
		AA8F89DE2D2CD9D700A298E8 /* Satoshi-Variable.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-Variable.ttf"; sourceTree = "<group>"; };
		AA8F89DF2D2CD9D700A298E8 /* Satoshi-VariableItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Satoshi-VariableItalic.ttf"; sourceTree = "<group>"; };
		AAA344B92DAF09120028FDB0 /* Config-debug.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Config-debug.xcconfig"; sourceTree = "<group>"; };
		AAA344BD2DAF0BE90028FDB0 /* Config-release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Config-release.xcconfig"; sourceTree = "<group>"; };
		AAA344E12DAFC4520028FDB0 /* RapidMedicalDev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RapidMedicalDev.app; sourceTree = BUILT_PRODUCTS_DIR; };
		AACA521E2C8E7A360019BF60 /* RapidMedicalStaging.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RapidMedicalStaging.app; sourceTree = BUILT_PRODUCTS_DIR; };
		AAD630199D00F157475A8F38 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = RapidMedical/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		AEAFEF95993B5F1743B40DEB /* libPods-RapidMedical.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-RapidMedical.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B0F135F8323BEDBFAB99E68D /* Pods-RapidMedical.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RapidMedical.debug.xcconfig"; path = "Target Support Files/Pods-RapidMedical/Pods-RapidMedical.debug.xcconfig"; sourceTree = "<group>"; };
		C181DE32001BBF76053CBE2F /* libPods-RapidMedicalDev.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-RapidMedicalDev.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F01012CA16A80FE8CE4F0563 /* libPods-OneSignalNotificationServiceExtensionDev.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-OneSignalNotificationServiceExtensionDev.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		0C94CC7A2E0C320A001B0BA4 /* Exceptions for "OneSignalNotificationServiceExtension" folder in "OneSignalNotificationServiceExtensionDev" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 0C94CC702E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev */;
		};
		AA2AF29E2DEF718B004FF500 /* Exceptions for "OneSignalNotificationServiceExtension" folder in "OneSignalNotificationServiceExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = AA2AF2952DEF718B004FF500 /* OneSignalNotificationServiceExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		AA2AF2972DEF718B004FF500 /* OneSignalNotificationServiceExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				AA2AF29E2DEF718B004FF500 /* Exceptions for "OneSignalNotificationServiceExtension" folder in "OneSignalNotificationServiceExtension" target */,
				0C94CC7A2E0C320A001B0BA4 /* Exceptions for "OneSignalNotificationServiceExtension" folder in "OneSignalNotificationServiceExtensionDev" target */,
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = OneSignalNotificationServiceExtension;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C94CC732E0C320A001B0BA4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C7D797EF5AA777ACCFCD464A /* libPods-OneSignalNotificationServiceExtensionDev.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				53B7B354A245AB4BBC5090BD /* libPods-RapidMedical.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AA2AF2932DEF718B004FF500 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				CDB47483DFBC9D3FDF9C4386 /* libPods-OneSignalNotificationServiceExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AAA344CA2DAFC4520028FDB0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				10C7CA3A5226D2BCFEF0D936 /* libPods-RapidMedicalDev.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AACA52122C8E7A360019BF60 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* RapidMedicalTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* RapidMedicalTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = RapidMedicalTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* RapidMedical */ = {
			isa = PBXGroup;
			children = (
				AA2AF28F2DEF7020004FF500 /* RapidMedical.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				AAD630199D00F157475A8F38 /* PrivacyInfo.xcprivacy */,
			);
			name = RapidMedical;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AA7FBABC2DAFF02F00588FD4 /* hermes.xcframework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				70C3A3B4D157029611C0764D /* libPods-OneSignalNotificationServiceExtension.a */,
				F01012CA16A80FE8CE4F0563 /* libPods-OneSignalNotificationServiceExtensionDev.a */,
				AEAFEF95993B5F1743B40DEB /* libPods-RapidMedical.a */,
				C181DE32001BBF76053CBE2F /* libPods-RapidMedicalDev.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		340AB4602DDCA90F00BBB6D5 /* mapboxnavigation */ = {
			isa = PBXGroup;
			children = (
				3498E7802DDCB117001585F0 /* MapboxNavigationViewManager.m */,
				3498E7782DDCAA4C001585F0 /* MapboxNavigationViewManager.swift */,
				3498E77C2DDCAEA9001585F0 /* MapboxNavigationView.swift */,
				3498E7892DDCB235001585F0 /* MapboxNavigation-Bridging-Header.h */,
			);
			path = mapboxnavigation;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				AA2AF2912DEF705E004FF500 /* RapidMedicalStaging.entitlements */,
				AA2AF2902DEF7058004FF500 /* RapidMedicalDev.entitlements */,
				340AB4602DDCA90F00BBB6D5 /* mapboxnavigation */,
				AAA344BD2DAF0BE90028FDB0 /* Config-release.xcconfig */,
				AAA344B92DAF09120028FDB0 /* Config-debug.xcconfig */,
				8364BE152D5CA8AE00DF7287 /* assets */,
				83E213502D5CA32600368ACD /* main.jsbundle */,
				AA8F89D32D2CD96D00A298E8 /* Resources */,
				13B07FAE1A68108700A75B9A /* RapidMedical */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* RapidMedicalTests */,
				AA2AF2972DEF718B004FF500 /* OneSignalNotificationServiceExtension */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* RapidMedical.app */,
				00E356EE1AD99517003FC87E /* RapidMedicalTests.xctest */,
				AACA521E2C8E7A360019BF60 /* RapidMedicalStaging.app */,
				AAA344E12DAFC4520028FDB0 /* RapidMedicalDev.app */,
				AA2AF2962DEF718B004FF500 /* OneSignalNotificationServiceExtension.appex */,
				0C94CC792E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AA8F89D32D2CD96D00A298E8 /* Resources */ = {
			isa = PBXGroup;
			children = (
				AA8F89D42D2CD9D700A298E8 /* Satoshi-Black.ttf */,
				AA8F89D52D2CD9D700A298E8 /* Satoshi-BlackItalic.ttf */,
				AA8F89D62D2CD9D700A298E8 /* Satoshi-Bold.ttf */,
				AA8F89D72D2CD9D700A298E8 /* Satoshi-BoldItalic.ttf */,
				AA8F89D82D2CD9D700A298E8 /* Satoshi-Italic.ttf */,
				AA8F89D92D2CD9D700A298E8 /* Satoshi-Light.ttf */,
				AA8F89DA2D2CD9D700A298E8 /* Satoshi-LightItalic.ttf */,
				AA8F89DB2D2CD9D700A298E8 /* Satoshi-Medium.ttf */,
				AA8F89DC2D2CD9D700A298E8 /* Satoshi-MediumItalic.ttf */,
				AA8F89DD2D2CD9D700A298E8 /* Satoshi-Regular.ttf */,
				AA8F89DE2D2CD9D700A298E8 /* Satoshi-Variable.ttf */,
				AA8F89DF2D2CD9D700A298E8 /* Satoshi-VariableItalic.ttf */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				1EA4BBE01BD76005B0310D9C /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */,
				338D505FB23D241BE6542426 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */,
				78463E70F5CC22651BA37995 /* Pods-OneSignalNotificationServiceExtensionDev.debug.xcconfig */,
				A766C19011B0C52469356D5E /* Pods-OneSignalNotificationServiceExtensionDev.release.xcconfig */,
				B0F135F8323BEDBFAB99E68D /* Pods-RapidMedical.debug.xcconfig */,
				35DB149155C63963110D1A30 /* Pods-RapidMedical.release.xcconfig */,
				35274856859E117550E0248A /* Pods-RapidMedicalDev.debug.xcconfig */,
				7406721B93EA195E559A50A9 /* Pods-RapidMedicalDev.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* RapidMedicalTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "RapidMedicalTests" */;
			buildPhases = (
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = RapidMedicalTests;
			productName = RapidMedicalTests;
			productReference = 00E356EE1AD99517003FC87E /* RapidMedicalTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		0C94CC702E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0C94CC762E0C320A001B0BA4 /* Build configuration list for PBXNativeTarget "OneSignalNotificationServiceExtensionDev" */;
			buildPhases = (
				48E4DAF3D74E501D3F388702 /* [CP] Check Pods Manifest.lock */,
				0C94CC722E0C320A001B0BA4 /* Sources */,
				0C94CC732E0C320A001B0BA4 /* Frameworks */,
				0C94CC752E0C320A001B0BA4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				AA2AF2972DEF718B004FF500 /* OneSignalNotificationServiceExtension */,
			);
			name = OneSignalNotificationServiceExtensionDev;
			productName = OneSignalNotificationServiceExtension;
			productReference = 0C94CC792E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		13B07F861A680F5B00A75B9A /* RapidMedical */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "RapidMedical" */;
			buildPhases = (
				4E6970D7BD0DBD82C81E7F27 /* [CP] Check Pods Manifest.lock */,
				346A84692DDF150400A40C1B /* Load Env Vars into Info.plist */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				AA2AF2A22DEF718B004FF500 /* Embed Foundation Extensions */,
				612B36A6F7774E4F9412DC5E /* Upload Debug Symbols to Sentry */,
				7A914899877DD67E26177E42 /* [CP] Embed Pods Frameworks */,
				5D262629CE47141552DB1A17 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AA2AF29C2DEF718B004FF500 /* PBXTargetDependency */,
			);
			name = RapidMedical;
			productName = RapidMedical;
			productReference = 13B07F961A680F5B00A75B9A /* RapidMedical.app */;
			productType = "com.apple.product-type.application";
		};
		AA2AF2952DEF718B004FF500 /* OneSignalNotificationServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AA2AF29F2DEF718B004FF500 /* Build configuration list for PBXNativeTarget "OneSignalNotificationServiceExtension" */;
			buildPhases = (
				E5B523185398DD38BC290654 /* [CP] Check Pods Manifest.lock */,
				AA2AF2922DEF718B004FF500 /* Sources */,
				AA2AF2932DEF718B004FF500 /* Frameworks */,
				AA2AF2942DEF718B004FF500 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				AA2AF2972DEF718B004FF500 /* OneSignalNotificationServiceExtension */,
			);
			name = OneSignalNotificationServiceExtension;
			productName = OneSignalNotificationServiceExtension;
			productReference = AA2AF2962DEF718B004FF500 /* OneSignalNotificationServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		AAA344C52DAFC4520028FDB0 /* RapidMedicalDev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AAA344DE2DAFC4520028FDB0 /* Build configuration list for PBXNativeTarget "RapidMedicalDev" */;
			buildPhases = (
				C1140E2A9D3CA7A565057D6C /* [CP] Check Pods Manifest.lock */,
				0C72EE382E0C026500BB7132 /* Load Env Vars into Info.plist */,
				AAA344C72DAFC4520028FDB0 /* Sources */,
				AAA344CA2DAFC4520028FDB0 /* Frameworks */,
				AAA344CC2DAFC4520028FDB0 /* Resources */,
				AAA344DA2DAFC4520028FDB0 /* Bundle React Native code and images */,
				0C94CC8D2E0C35B5001B0BA4 /* Embed Foundation Extensions */,
				AAA344DB2DAFC4520028FDB0 /* Upload Debug Symbols to Sentry */,
				C7D03F66341F67FCF1EE16C0 /* [CP] Embed Pods Frameworks */,
				DD7D7BA8CEC47893314EE2A4 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0C94CC8C2E0C35B5001B0BA4 /* PBXTargetDependency */,
			);
			name = RapidMedicalDev;
			productName = RapidMedical;
			productReference = AAA344E12DAFC4520028FDB0 /* RapidMedicalDev.app */;
			productType = "com.apple.product-type.application";
		};
		AACA520D2C8E7A360019BF60 /* RapidMedicalStaging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AACA521B2C8E7A360019BF60 /* Build configuration list for PBXNativeTarget "RapidMedicalStaging" */;
			buildPhases = (
				AACA520F2C8E7A360019BF60 /* Sources */,
				AACA52122C8E7A360019BF60 /* Frameworks */,
				AACA52142C8E7A360019BF60 /* Resources */,
				AACA52182C8E7A360019BF60 /* Bundle React Native code and images */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RapidMedicalStaging;
			productName = RapidMedical;
			productReference = AACA521E2C8E7A360019BF60 /* RapidMedicalStaging.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1620;
					};
					AA2AF2952DEF718B004FF500 = {
						CreatedOnToolsVersion = 16.2;
					};
					AAA344C52DAFC4520028FDB0 = {
						LastSwiftMigration = 1620;
					};
					AACA520D2C8E7A360019BF60 = {
						LastSwiftMigration = 1620;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "RapidMedical" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* RapidMedical */,
				AAA344C52DAFC4520028FDB0 /* RapidMedicalDev */,
				AACA520D2C8E7A360019BF60 /* RapidMedicalStaging */,
				00E356ED1AD99517003FC87E /* RapidMedicalTests */,
				AA2AF2952DEF718B004FF500 /* OneSignalNotificationServiceExtension */,
				0C94CC702E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C94CC752E0C320A001B0BA4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8364BE162D5CA8AE00DF7287 /* assets in Resources */,
				83E213512D5CA32600368ACD /* main.jsbundle in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				A07350928CD1F18E4CA9AF66 /* PrivacyInfo.xcprivacy in Resources */,
				AA8F89F92D2CD9D700A298E8 /* Satoshi-Light.ttf in Resources */,
				AA8F89FA2D2CD9D700A298E8 /* Satoshi-Bold.ttf in Resources */,
				AA8F89FC2D2CD9D700A298E8 /* Satoshi-Regular.ttf in Resources */,
				AA8F89FD2D2CD9D700A298E8 /* Satoshi-Variable.ttf in Resources */,
				AA8F89FE2D2CD9D700A298E8 /* Satoshi-Black.ttf in Resources */,
				AA8F8A002D2CD9D700A298E8 /* Satoshi-Medium.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AA2AF2942DEF718B004FF500 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AAA344CC2DAFC4520028FDB0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AAA344CD2DAFC4520028FDB0 /* assets in Resources */,
				AAA344CE2DAFC4520028FDB0 /* main.jsbundle in Resources */,
				AAA344CF2DAFC4520028FDB0 /* LaunchScreen.storyboard in Resources */,
				AAA344D02DAFC4520028FDB0 /* Images.xcassets in Resources */,
				AAA344D12DAFC4520028FDB0 /* PrivacyInfo.xcprivacy in Resources */,
				AAA344D22DAFC4520028FDB0 /* Satoshi-Light.ttf in Resources */,
				AAA344D42DAFC4520028FDB0 /* Satoshi-Bold.ttf in Resources */,
				AAA344D52DAFC4520028FDB0 /* Satoshi-Regular.ttf in Resources */,
				AAA344D62DAFC4520028FDB0 /* Satoshi-Variable.ttf in Resources */,
				AAA344D72DAFC4520028FDB0 /* Satoshi-Black.ttf in Resources */,
				AAA344D82DAFC4520028FDB0 /* Satoshi-Medium.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AACA52142C8E7A360019BF60 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AA04FB562D30F3BE00A7AC30 /* BuildFile in Resources */,
				AACA52152C8E7A360019BF60 /* LaunchScreen.storyboard in Resources */,
				AACA52162C8E7A360019BF60 /* Images.xcassets in Resources */,
				AACA52172C8E7A360019BF60 /* PrivacyInfo.xcprivacy in Resources */,
				AA8F89E12D2CD9D700A298E8 /* Satoshi-Light.ttf in Resources */,
				AA8F89E22D2CD9D700A298E8 /* Satoshi-Bold.ttf in Resources */,
				AA8F89E42D2CD9D700A298E8 /* Satoshi-Regular.ttf in Resources */,
				AA8F89E52D2CD9D700A298E8 /* Satoshi-Variable.ttf in Resources */,
				AA8F89E62D2CD9D700A298E8 /* Satoshi-Black.ttf in Resources */,
				AA8F89E82D2CD9D700A298E8 /* Satoshi-Medium.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n#/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		0C72EE382E0C026500BB7132 /* Load Env Vars into Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Load Env Vars into Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "echo \"Injecting MAPBOX_ACCESS_TOKEN from .env.dev into Info.plist\"\n\n# Load .env.dev file from project root\nif [ -f \"$PROJECT_DIR/../.env.dev\" ]; then\n  export $(grep -v '^#' \"$PROJECT_DIR/../.env.dev\" | xargs)\nelse\n  echo \".env.dev file not found at $PROJECT_DIR/../.env.dev\"\n  exit 1\nfi\n\n# Check if MAPBOX_ACCESS_TOKEN is set\nif [ -z \"$MAPBOX_ACCESS_TOKEN\" ]; then\n  echo \"MAPBOX_ACCESS_TOKEN is not set in .env.dev\"\n  exit 1\nfi\n\n# Add or update the MBXAccessToken key in Info.plist\nPLIST=\"${PROJECT_DIR}/RapidMedical/Info.plist\"\n\n/usr/libexec/PlistBuddy -c \"Delete :MBXAccessToken\" \"$PLIST\" 2>/dev/null\n/usr/libexec/PlistBuddy -c \"Add :MBXAccessToken string $MAPBOX_ACCESS_TOKEN\" \"$PLIST\"\n";
		};
		346A84692DDF150400A40C1B /* Load Env Vars into Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Load Env Vars into Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "echo \"Injecting MAPBOX_ACCESS_TOKEN from .env into Info.plist\"\n\n# Load .env file from project root\nif [ -f \"$PROJECT_DIR/../.env\" ]; then\n  export $(grep -v '^#' \"$PROJECT_DIR/../.env\" | xargs)\nelse\n  echo \".env file not found at $PROJECT_DIR/../.env\"\n  exit 1\nfi\n\n# Check if MAPBOX_ACCESS_TOKEN is set\nif [ -z \"$MAPBOX_ACCESS_TOKEN\" ]; then\n  echo \"MAPBOX_ACCESS_TOKEN is not set in .env\"\n  exit 1\nfi\n\n# Add or update the MBXAccessToken key in Info.plist\nPLIST=\"${PROJECT_DIR}/${TARGET_NAME}/Info.plist\"\n\n/usr/libexec/PlistBuddy -c \"Delete :MBXAccessToken\" \"$PLIST\" 2>/dev/null\n/usr/libexec/PlistBuddy -c \"Add :MBXAccessToken string $MAPBOX_ACCESS_TOKEN\" \"$PLIST\"\n\n";
		};
		48E4DAF3D74E501D3F388702 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-OneSignalNotificationServiceExtensionDev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4E6970D7BD0DBD82C81E7F27 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RapidMedical-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5D262629CE47141552DB1A17 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedical/Pods-RapidMedical-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedical/Pods-RapidMedical-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RapidMedical/Pods-RapidMedical-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		612B36A6F7774E4F9412DC5E /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh\n";
		};
		7A914899877DD67E26177E42 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedical/Pods-RapidMedical-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedical/Pods-RapidMedical-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RapidMedical/Pods-RapidMedical-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AAA344DA2DAFC4520028FDB0 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n#/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		AAA344DB2DAFC4520028FDB0 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh\n";
		};
		AACA52182C8E7A360019BF60 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		C1140E2A9D3CA7A565057D6C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RapidMedicalDev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C7D03F66341F67FCF1EE16C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DD7D7BA8CEC47893314EE2A4 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RapidMedicalDev/Pods-RapidMedicalDev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E5B523185398DD38BC290654 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-OneSignalNotificationServiceExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* RapidMedicalTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C94CC722E0C320A001B0BA4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3498E7792DDCAA4C001585F0 /* MapboxNavigationViewManager.swift in Sources */,
				3498E7832DDCB11F001585F0 /* MapboxNavigationViewManager.m in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				3498E77D2DDCAEA9001585F0 /* MapboxNavigationView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AA2AF2922DEF718B004FF500 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AAA344C72DAFC4520028FDB0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3498E77A2DDCAA4C001585F0 /* MapboxNavigationViewManager.swift in Sources */,
				3498E7812DDCB11F001585F0 /* MapboxNavigationViewManager.m in Sources */,
				AAA344C82DAFC4520028FDB0 /* AppDelegate.mm in Sources */,
				AAA344C92DAFC4520028FDB0 /* main.m in Sources */,
				3498E77F2DDCAEA9001585F0 /* MapboxNavigationView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AACA520F2C8E7A360019BF60 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3498E77B2DDCAA4C001585F0 /* MapboxNavigationViewManager.swift in Sources */,
				3498E7822DDCB11F001585F0 /* MapboxNavigationViewManager.m in Sources */,
				AACA52102C8E7A360019BF60 /* AppDelegate.mm in Sources */,
				AACA52112C8E7A360019BF60 /* main.m in Sources */,
				3498E77E2DDCAEA9001585F0 /* MapboxNavigationView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* RapidMedical */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		0C94CC8C2E0C35B5001B0BA4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0C94CC702E0C320A001B0BA4 /* OneSignalNotificationServiceExtensionDev */;
			targetProxy = 0C94CC8B2E0C35B5001B0BA4 /* PBXContainerItemProxy */;
		};
		AA2AF29C2DEF718B004FF500 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AA2AF2952DEF718B004FF500 /* OneSignalNotificationServiceExtension */;
			targetProxy = AA2AF29B2DEF718B004FF500 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = RapidMedicalTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RapidMedical.app/RapidMedical";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = RapidMedicalTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RapidMedical.app/RapidMedical";
			};
			name = Release;
		};
		0C94CC772E0C320A001B0BA4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 78463E70F5CC22651BA37995 /* Pods-OneSignalNotificationServiceExtensionDev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = OneSignalNotificationServiceExtension/OneSignalNotificationServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = OneSignalNotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = OneSignalNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.dev.OneSignalNotificationServiceExtensionDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		0C94CC782E0C320A001B0BA4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A766C19011B0C52469356D5E /* Pods-OneSignalNotificationServiceExtensionDev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = OneSignalNotificationServiceExtension/OneSignalNotificationServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = OneSignalNotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = OneSignalNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.dev.OneSignalNotificationServiceExtensionDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA344B92DAF09120028FDB0 /* Config-debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = RapidMedical/RapidMedical.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 93;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = RapidMedical/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Rapid Medical";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app;
				PRODUCT_NAME = RapidMedical;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "mapboxnavigation/MapboxNavigation-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA344BD2DAF0BE90028FDB0 /* Config-release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = RapidMedical/RapidMedical.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 93;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				INFOPLIST_FILE = RapidMedical/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Rapid Medical";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app;
				PRODUCT_NAME = RapidMedical;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "mapboxnavigation/MapboxNavigation-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		AA2AF2A02DEF718B004FF500 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1EA4BBE01BD76005B0310D9C /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = OneSignalNotificationServiceExtension/OneSignalNotificationServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = OneSignalNotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = OneSignalNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.OneSignalNotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AA2AF2A12DEF718B004FF500 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 338D505FB23D241BE6542426 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = OneSignalNotificationServiceExtension/OneSignalNotificationServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = OneSignalNotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = OneSignalNotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.OneSignalNotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AAA344DF2DAFC4520028FDB0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA344B92DAF09120028FDB0 /* Config-debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconDev;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = RapidMedicalDev.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 92;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = RapidMedical/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Rapid Medical - Dev";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "mapboxnavigation/MapboxNavigation-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		AAA344E02DAFC4520028FDB0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA344BD2DAF0BE90028FDB0 /* Config-release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconDev;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = RapidMedicalDev.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 92;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				INFOPLIST_FILE = RapidMedical/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Rapid Medical - Dev";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.5;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "mapboxnavigation/MapboxNavigation-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		AACA521C2C8E7A360019BF60 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA344B92DAF09120028FDB0 /* Config-debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconStaging;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = RapidMedicalStaging.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "RapidMedicalStaging-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Rapid Medical - Staging";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.staging;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		AACA521D2C8E7A360019BF60 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AAA344BD2DAF0BE90028FDB0 /* Config-release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconStaging;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = RapidMedicalStaging.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6954U4AVL5;
				INFOPLIST_FILE = "RapidMedicalStaging-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Rapid Medical - Staging";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.rapidmedical.app.staging;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "RapidMedicalTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0C94CC762E0C320A001B0BA4 /* Build configuration list for PBXNativeTarget "OneSignalNotificationServiceExtensionDev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0C94CC772E0C320A001B0BA4 /* Debug */,
				0C94CC782E0C320A001B0BA4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "RapidMedical" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "RapidMedical" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AA2AF29F2DEF718B004FF500 /* Build configuration list for PBXNativeTarget "OneSignalNotificationServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AA2AF2A02DEF718B004FF500 /* Debug */,
				AA2AF2A12DEF718B004FF500 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AAA344DE2DAFC4520028FDB0 /* Build configuration list for PBXNativeTarget "RapidMedicalDev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AAA344DF2DAFC4520028FDB0 /* Debug */,
				AAA344E02DAFC4520028FDB0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AACA521B2C8E7A360019BF60 /* Build configuration list for PBXNativeTarget "RapidMedicalStaging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AACA521C2C8E7A360019BF60 /* Debug */,
				AACA521D2C8E7A360019BF60 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
