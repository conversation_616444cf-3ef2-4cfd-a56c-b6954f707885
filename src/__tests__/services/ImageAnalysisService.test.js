import ImageAnalysisService from '../../services/ImageAnalysisService';

// Mock OpenCV.js
jest.mock('@techstark/opencv-js', () => ({
  __esModule: true,
  default: Promise.resolve({
    matFromImageData: jest.fn(),
    cvtColor: jest.fn(),
    Laplacian: jest.fn(),
    meanStdDev: jest.fn(),
    COLOR_RGBA2GRAY: 0,
    CV_64F: 6,
    Mat: jest.fn().mockImplementation(() => ({
      delete: jest.fn(),
      doubleAt: jest.fn().mockReturnValue(10), // Mock standard deviation
    })),
  }),
}));

// Mock react-native-fs
jest.mock('react-native-fs', () => ({
  readFile: jest.fn().mockResolvedValue('base64mockdata'),
}));

describe('ImageAnalysisService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should initialize OpenCV successfully', async () => {
      await expect(ImageAnalysisService.initialize()).resolves.toBeDefined();
    });

    it('should only initialize once', async () => {
      await ImageAnalysisService.initialize();
      const cv1 = ImageAnalysisService.cv;
      await ImageAnalysisService.initialize();
      const cv2 = ImageAnalysisService.cv;
      expect(cv1).toBe(cv2);
    });
  });

  describe('Image Format Preparation', () => {
    // Mock ImageData for testing
    const mockImageData = {
      data: new Uint8ClampedArray([255, 0, 0, 255]), // Red pixel
      width: 1,
      height: 1,
    };

    // Mock DOM elements for React Native environment
    beforeEach(() => {
      global.document = {
        createElement: jest.fn().mockReturnValue({
          getContext: jest.fn().mockReturnValue({
            drawImage: jest.fn(),
            getImageData: jest.fn().mockReturnValue(mockImageData),
          }),
          width: 0,
          height: 0,
        }),
      };
      global.window = {
        Image: jest.fn().mockImplementation(() => ({
          onload: null,
          onerror: null,
          src: '',
        })),
      };
    });

    it('should prepare image from file path', async () => {
      // This test would need actual file system mocking
      // For now, we'll test that the method exists and handles errors
      const result = await ImageAnalysisService.prepareImageForAnalysis(
        '/test/path.jpg',
        'path'
      ).catch(e => e);
      
      expect(result).toBeDefined();
    });

    it('should handle unsupported input types', async () => {
      await expect(
        ImageAnalysisService.prepareImageForAnalysis('test', 'unsupported')
      ).rejects.toThrow('Unsupported input type: unsupported');
    });
  });

  describe('Blur Detection', () => {
    const mockBlurResult = {
      variance: 150.25,
      interpretation: 'Sharp',
      isBlurry: false,
      threshold: 100,
      analysisType: 'blur_detection',
    };

    it('should calculate blur variance', async () => {
      // Mock the internal method to return a known value
      jest.spyOn(ImageAnalysisService, 'getLaplacianVarianceFromImageData')
        .mockResolvedValue(150.25);
      jest.spyOn(ImageAnalysisService, 'prepareImageForAnalysis')
        .mockResolvedValue({});

      const result = await ImageAnalysisService.calculateBlurVariance('/test/path.jpg', 'path');
      expect(result).toBe(150.25);
    });

    it('should analyze blur with correct interpretation', async () => {
      jest.spyOn(ImageAnalysisService, 'calculateBlurVariance')
        .mockResolvedValue(150.25);

      const result = await ImageAnalysisService.analyzeBlur('/test/path.jpg', 'path');
      
      expect(result).toEqual(expect.objectContaining({
        variance: 150.25,
        interpretation: 'Sharp',
        isBlurry: false,
        analysisType: 'blur_detection',
      }));
    });

    it('should correctly identify blurry images', async () => {
      jest.spyOn(ImageAnalysisService, 'calculateBlurVariance')
        .mockResolvedValue(50); // Below threshold

      const result = await ImageAnalysisService.analyzeBlur('/test/blurry.jpg', 'path');
      
      expect(result.isBlurry).toBe(true);
      expect(result.interpretation).toBe('Blurry');
    });

    it('should correctly identify sharp images', async () => {
      jest.spyOn(ImageAnalysisService, 'calculateBlurVariance')
        .mockResolvedValue(250); // Above threshold

      const result = await ImageAnalysisService.analyzeBlur('/test/sharp.jpg', 'path');
      
      expect(result.isBlurry).toBe(false);
      expect(result.interpretation).toBe('Sharp');
    });

    it('should handle custom thresholds', async () => {
      jest.spyOn(ImageAnalysisService, 'calculateBlurVariance')
        .mockResolvedValue(120);

      const result = await ImageAnalysisService.isImageBlurry('/test/image.jpg', 'path', 150);
      expect(result).toBe(true); // 120 < 150
    });
  });

  describe('Image Quality Analysis', () => {
    it('should provide comprehensive quality analysis', async () => {
      jest.spyOn(ImageAnalysisService, 'analyzeBlur')
        .mockResolvedValue({
          variance: 200,
          interpretation: 'Sharp',
          isBlurry: false,
          threshold: 100,
          analysisType: 'blur_detection',
        });

      const result = await ImageAnalysisService.analyzeImageQuality('/test/image.jpg', 'path');
      
      expect(result).toEqual(expect.objectContaining({
        blur: expect.objectContaining({
          variance: 200,
          interpretation: 'Sharp',
          isBlurry: false,
        }),
        overall: expect.objectContaining({
          quality: 'Good',
          recommendation: 'Image quality is acceptable',
        }),
      }));
    });
  });

  describe('Error Handling', () => {
    it('should return null for failed analysis', async () => {
      jest.spyOn(ImageAnalysisService, 'prepareImageForAnalysis')
        .mockRejectedValue(new Error('File not found'));

      const result = await ImageAnalysisService.calculateBlurVariance('/nonexistent.jpg', 'path');
      expect(result).toBeNull();
    });

    it('should handle OpenCV errors gracefully', async () => {
      jest.spyOn(ImageAnalysisService, 'getLaplacianVarianceFromImageData')
        .mockRejectedValue(new Error('OpenCV error'));
      jest.spyOn(ImageAnalysisService, 'prepareImageForAnalysis')
        .mockResolvedValue({});

      const result = await ImageAnalysisService.calculateBlurVariance('/test/image.jpg', 'path');
      expect(result).toBeNull();
    });
  });

  describe('Blur Interpretation Categories', () => {
    const testCases = [
      { variance: 25, expected: 'Very Blurry' },
      { variance: 75, expected: 'Blurry' },
      { variance: 150, expected: 'Slightly Blurry' },
      { variance: 250, expected: 'Sharp' },
    ];

    testCases.forEach(({ variance, expected }) => {
      it(`should interpret variance ${variance} as ${expected}`, async () => {
        jest.spyOn(ImageAnalysisService, 'calculateBlurVariance')
          .mockResolvedValue(variance);

        const result = await ImageAnalysisService.analyzeBlur('/test/image.jpg', 'path');
        expect(result.interpretation).toBe(expected);
      });
    });
  });
});
