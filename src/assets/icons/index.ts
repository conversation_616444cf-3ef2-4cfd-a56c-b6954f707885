// No need to convert to React SVG components as they have multiple colors in svg and not being used in multiple places
import LogoWithText from '~/assets/logo.svg';
import CircleCheck from '~/assets/icons/timeline/circle-check.svg';
import RMBlueHeaderIcon from '~/assets/icons/header/RM-header-icon-large.svg';
import Warning from '~/assets/icons/alerts/warning.svg';
import BrushWithBackground from '~/assets/icons/brush.svg';
import PhotoWithBackground from '~/assets/icons/cards/photo.svg';
import ParcelWithBackground from '~/assets/icons/cards/parcel.svg';
import LogoutWithBackground from '~/assets/icons/alerts/logout.svg';
import BoxWithBackground from '~/assets/icons/confirmations/pickup-dropoff-completed.svg';
import StopWatchWithBackground from '~/assets/icons/alerts/stop-closure-warning.svg';
import GreenCheckWithBackground from '~/assets/icons/confirmations/route-completed.svg';
import WifiWarningWithBackground from '~/assets/icons/alerts/wifi-warning.svg';
import RouteWithBackground from '~/assets/icons/confirmations/route-with-background.svg';
import DustbinWithBackground from '~/assets/icons/header/dustbin.svg';
import GeofenceWarningWithBackground from '~/assets/icons/confirmations/geofence-warning.svg';
import RedThermometer from '~/assets/icons/temperature/redThermometer.svg';
import HotWeather from '~/assets/icons/temperature/hotWeather.svg';
import ColdWeather from '~/assets/icons/temperature/coldWeather.svg';
import GridView from '~/assets/icons/temperature/gridView.svg';
import Thermometer from '~/assets/icons/temperature/thermometer.svg';

export {
  Warning,
  CircleCheck,
  LogoWithText,
  RMBlueHeaderIcon,
  BrushWithBackground,
  LogoutWithBackground,
  ParcelWithBackground,
  PhotoWithBackground,
  StopWatchWithBackground,
  GreenCheckWithBackground,
  BoxWithBackground,
  WifiWarningWithBackground,
  RouteWithBackground,
  DustbinWithBackground,
  GeofenceWarningWithBackground,
  RedThermometer,
  HotWeather,
  ColdWeather,
  GridView,
  Thermometer,
};
