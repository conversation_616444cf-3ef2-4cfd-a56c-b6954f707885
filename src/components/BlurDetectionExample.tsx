import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import ImageAnalysisService from '../services/ImageAnalysisService';

interface BlurAnalysisResult {
  variance: number;
  interpretation: string;
  isBlurry: boolean;
  threshold: number;
  analysisType: string;
}

interface ImageQualityResult {
  blur: BlurAnalysisResult | null;
  overall: {
    quality: string;
    recommendation: string;
  };
}

const ImageAnalysisExample: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [analysisResult, setAnalysisResult] = useState<BlurAnalysisResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const selectImage = () => {
    // For now, we'll simulate image selection
    // In a real app, you'd use react-native-image-picker or similar
    Alert.alert(
      'Image Selection',
      'In a real implementation, this would open the image picker. For now, please provide an image path.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Use Sample',
          onPress: () => {
            // Simulate selecting an image
            setSelectedImage('/path/to/sample/image.jpg');
            setAnalysisResult(null);
          },
        },
      ]
    );
  };

  const analyzeImage = async () => {
    if (!selectedImage) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    setIsProcessing(true);

    try {
      // Use the new ImageAnalysisService
      // The service handles all the image format conversion internally
      const result = await ImageAnalysisService.analyzeBlur(selectedImage, 'path') as BlurAnalysisResult;
      setAnalysisResult(result);

    } catch (error) {
      console.error('Error analyzing image:', error);
      Alert.alert('Error', 'Failed to analyze image');
    } finally {
      setIsProcessing(false);
    }
  };

  const analyzeImageQuality = async () => {
    if (!selectedImage) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    setIsProcessing(true);

    try {
      // Comprehensive image quality analysis
      const result = await ImageAnalysisService.analyzeImageQuality(selectedImage, 'path') as ImageQualityResult;
      console.log('Full image quality analysis:', result);

      // For this example, we'll just show the blur analysis
      if (result?.blur) {
        setAnalysisResult(result.blur);
      }

    } catch (error) {
      console.error('Error analyzing image quality:', error);
      Alert.alert('Error', 'Failed to analyze image quality');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Image Analysis Test</Text>

      <TouchableOpacity style={styles.button} onPress={selectImage}>
        <Text style={styles.buttonText}>Select Image</Text>
      </TouchableOpacity>

      {selectedImage && (
        <View style={styles.imageContainer}>
          <Text style={styles.imagePath}>Selected: {selectedImage}</Text>

          <TouchableOpacity
            style={[styles.button, styles.analyzeButton]}
            onPress={analyzeImage}
            disabled={isProcessing}
          >
            <Text style={styles.buttonText}>
              {isProcessing ? 'Analyzing...' : 'Analyze Blur'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.qualityButton]}
            onPress={analyzeImageQuality}
            disabled={isProcessing}
          >
            <Text style={styles.buttonText}>
              {isProcessing ? 'Analyzing...' : 'Full Quality Analysis'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {analysisResult && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Analysis Result:</Text>
          <Text style={styles.resultText}>
            Variance: {analysisResult.variance.toFixed(2)}
          </Text>
          <Text style={styles.resultText}>
            Status: {analysisResult.interpretation}
          </Text>
          <Text style={styles.resultText}>
            Threshold: {analysisResult.threshold}
          </Text>
          <Text style={[
            styles.resultText,
            { color: analysisResult.isBlurry ? '#ff4444' : '#44ff44' }
          ]}>
            {analysisResult.isBlurry ? '❌ Image is blurry' : '✅ Image is sharp'}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
    minWidth: 200,
  },
  analyzeButton: {
    backgroundColor: '#34C759',
  },
  qualityButton: {
    backgroundColor: '#FF9500',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  imagePath: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    textAlign: 'center',
  },
  resultContainer: {
    backgroundColor: '#f0f0f0',
    padding: 20,
    borderRadius: 8,
    marginTop: 20,
    alignItems: 'center',
    minWidth: 300,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 16,
    marginVertical: 2,
    textAlign: 'center',
  },
});

export default ImageAnalysisExample;
