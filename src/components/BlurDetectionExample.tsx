import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, Image, Alert, StyleSheet } from 'react-native';
import { launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import BlurDetectionService from '../services/BlurDetectionService';

const BlurDetectionExample: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [blurResult, setBlurResult] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const selectImage = () => {
    const options = {
      mediaType: 'photo' as MediaType,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.didCancel || response.errorMessage) {
        return;
      }

      if (response.assets && response.assets[0]) {
        const imageUri = response.assets[0].uri;
        setSelectedImage(imageUri);
        setBlurResult(null);
      }
    });
  };

  const analyzeBlur = async () => {
    if (!selectedImage) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    setIsProcessing(true);
    
    try {
      // Convert image to ImageData
      const imageData = await getImageDataFromUri(selectedImage);
      
      // Analyze blur
      const result = await BlurDetectionService.getBlurScore(imageData);
      setBlurResult(result);
      
    } catch (error) {
      console.error('Error analyzing blur:', error);
      Alert.alert('Error', 'Failed to analyze image blur');
    } finally {
      setIsProcessing(false);
    }
  };

  // Helper function to convert image URI to ImageData
  const getImageDataFromUri = (uri: string): Promise<ImageData> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
        if (imageData) {
          resolve(imageData);
        } else {
          reject(new Error('Failed to get image data'));
        }
      };
      
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = uri;
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Blur Detection Test</Text>
      
      <TouchableOpacity style={styles.button} onPress={selectImage}>
        <Text style={styles.buttonText}>Select Image</Text>
      </TouchableOpacity>

      {selectedImage && (
        <View style={styles.imageContainer}>
          <Image source={{ uri: selectedImage }} style={styles.image} />
          
          <TouchableOpacity 
            style={[styles.button, styles.analyzeButton]} 
            onPress={analyzeBlur}
            disabled={isProcessing}
          >
            <Text style={styles.buttonText}>
              {isProcessing ? 'Analyzing...' : 'Analyze Blur'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {blurResult && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Blur Analysis Result:</Text>
          <Text style={styles.resultText}>
            Variance: {blurResult.variance.toFixed(2)}
          </Text>
          <Text style={styles.resultText}>
            Status: {blurResult.interpretation}
          </Text>
          <Text style={[
            styles.resultText, 
            { color: blurResult.isBlurry ? '#ff4444' : '#44ff44' }
          ]}>
            {blurResult.isBlurry ? '❌ Image is blurry' : '✅ Image is sharp'}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
  },
  analyzeButton: {
    backgroundColor: '#34C759',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  imageContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  image: {
    width: 300,
    height: 200,
    resizeMode: 'contain',
    marginBottom: 15,
  },
  resultContainer: {
    backgroundColor: '#f0f0f0',
    padding: 20,
    borderRadius: 8,
    marginTop: 20,
    alignItems: 'center',
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 16,
    marginVertical: 2,
  },
});

export default BlurDetectionExample;
