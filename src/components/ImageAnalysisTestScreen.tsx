import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  Alert, 
  StyleSheet 
} from 'react-native';
import ImageAnalysisService from '../services/ImageAnalysisService';

interface TestResult {
  path: string;
  variance: number;
  interpretation: string;
  isBlurry: boolean;
  analysisTime: number;
  timestamp: string;
}

const ImageAnalysisTestScreen: React.FC = () => {
  const [imagePath, setImagePath] = useState('');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzeImage = async () => {
    if (!imagePath.trim()) {
      Alert.alert('Error', 'Please enter an image path');
      return;
    }

    setIsAnalyzing(true);
    const startTime = Date.now();

    try {
      const result = await ImageAnalysisService.analyzeBlur(imagePath.trim(), 'path');
      const endTime = Date.now();

      if (result) {
        const testResult: TestResult = {
          path: imagePath.trim(),
          variance: result.variance,
          interpretation: result.interpretation,
          isBlurry: result.isBlurry,
          analysisTime: endTime - startTime,
          timestamp: new Date().toLocaleTimeString()
        };

        setTestResults(prev => [testResult, ...prev]);
        
        // Show quick result
        Alert.alert(
          'Analysis Complete',
          `Result: ${result.interpretation}\nVariance: ${result.variance.toFixed(2)}\nTime: ${endTime - startTime}ms`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Error', 'Analysis failed - check image path and format');
      }
    } catch (error) {
      console.error('Analysis error:', error);
      Alert.alert('Error', `Analysis failed: ${error.message}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const runBatchTest = async () => {
    // Sample paths - you'll need to update these with real image paths
    const testPaths = [
      '/path/to/sharp/image1.jpg',
      '/path/to/blurry/image1.jpg',
      '/path/to/sharp/image2.jpg',
      '/path/to/blurry/image2.jpg',
    ];

    Alert.alert(
      'Batch Test',
      `This will test ${testPaths.length} images. Update the testPaths array with real image paths first.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Run Test', 
          onPress: async () => {
            setIsAnalyzing(true);
            
            for (const path of testPaths) {
              try {
                const startTime = Date.now();
                const result = await ImageAnalysisService.analyzeBlur(path, 'path');
                const endTime = Date.now();

                if (result) {
                  const testResult: TestResult = {
                    path,
                    variance: result.variance,
                    interpretation: result.interpretation,
                    isBlurry: result.isBlurry,
                    analysisTime: endTime - startTime,
                    timestamp: new Date().toLocaleTimeString()
                  };
                  setTestResults(prev => [testResult, ...prev]);
                }
              } catch (error) {
                console.error(`Error testing ${path}:`, error);
              }
            }
            
            setIsAnalyzing(false);
          }
        }
      ]
    );
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getResultColor = (isBlurry: boolean) => {
    return isBlurry ? '#ff4444' : '#44ff44';
  };

  const getVarianceCategory = (variance: number) => {
    if (variance < 50) return 'Very Low';
    if (variance < 100) return 'Low';
    if (variance < 200) return 'Medium';
    if (variance < 500) return 'High';
    return 'Very High';
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Image Analysis Testing</Text>
      
      <View style={styles.inputSection}>
        <Text style={styles.label}>Image Path:</Text>
        <TextInput
          style={styles.textInput}
          value={imagePath}
          onChangeText={setImagePath}
          placeholder="/path/to/your/image.jpg"
          multiline
        />
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.analyzeButton]} 
            onPress={analyzeImage}
            disabled={isAnalyzing}
          >
            <Text style={styles.buttonText}>
              {isAnalyzing ? 'Analyzing...' : 'Analyze Image'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.batchButton]} 
            onPress={runBatchTest}
            disabled={isAnalyzing}
          >
            <Text style={styles.buttonText}>Batch Test</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.resultsSection}>
        <View style={styles.resultsHeader}>
          <Text style={styles.resultsTitle}>Test Results ({testResults.length})</Text>
          <TouchableOpacity style={styles.clearButton} onPress={clearResults}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>

        {testResults.map((result, index) => (
          <View key={index} style={styles.resultCard}>
            <Text style={styles.resultPath} numberOfLines={1}>
              📁 {result.path}
            </Text>
            <Text style={styles.resultTime}>
              🕒 {result.timestamp} ({result.analysisTime}ms)
            </Text>
            
            <View style={styles.resultMetrics}>
              <Text style={styles.resultVariance}>
                📊 Variance: {result.variance.toFixed(2)} ({getVarianceCategory(result.variance)})
              </Text>
              <Text style={styles.resultInterpretation}>
                📝 {result.interpretation}
              </Text>
              <Text style={[styles.resultStatus, { color: getResultColor(result.isBlurry) }]}>
                {result.isBlurry ? '❌ Blurry' : '✅ Sharp'}
              </Text>
            </View>
          </View>
        ))}

        {testResults.length === 0 && (
          <Text style={styles.noResults}>No test results yet. Enter an image path and tap "Analyze Image".</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 20,
    color: '#333',
  },
  inputSection: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 15,
    gap: 10,
  },
  button: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  analyzeButton: {
    backgroundColor: '#007AFF',
  },
  batchButton: {
    backgroundColor: '#FF9500',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsSection: {
    margin: 15,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  resultCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  resultPath: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  resultTime: {
    fontSize: 12,
    color: '#999',
    marginBottom: 10,
  },
  resultMetrics: {
    gap: 5,
  },
  resultVariance: {
    fontSize: 14,
    color: '#333',
  },
  resultInterpretation: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  resultStatus: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  noResults: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
    marginTop: 20,
  },
});

export default ImageAnalysisTestScreen;
