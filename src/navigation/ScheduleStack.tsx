import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import DailyScheduleScreen from '~/screens/schedule/DailyScheduleScreen';
import ThemedHeader from '~/components/headers/ThemedHeader';
import SelectDateScreen from '~/screens/schedule/SelectDateScreen';
import StopDetailScreen from '~/screens/schedule/StopDetailScreen';
import SelectServiceScreen from '~/screens/schedule/SelectServiceScreen';
import { Stop, TaskListItem } from '~/types/stops.types';
import colors from '~/styles/colors';
import SubHeader from '~/components/headers/SubHeader';
import en from '~/localization/en';
import { validateThatDataIsNotEmpty } from '~/utils/validation/data';
import { SyncServiceProvider } from '~/services/sync/syncServiceContext';
import { useSync } from '~/services/sync';
import ProtocolEngineStack from '~/navigation/ProtocolEngineStack';
import CameraViewScreen from '~/screens/CameraViewScreen';
import CollectSignature from '~/screens/CollectSignature';
import PhotoViewScreen, {
  PhotoViewScreenProps,
} from '~/screens/PhotoViewScreen';
import ConfirmationScreen from '~/screens/schedule/ConfirmationScreen';
import StopOverviewScreen from '~/screens/schedule/StopOverviewScreen';
import MapNavigationScreen from '~/screens/MapNavigationScreen';
import {
  ConfirmationType,
  ConfirmationSubType,
} from '~/types/confirmations.types';
import { Protocol } from '~/types/protocol.types';
import { ProtocolContextData } from '~/types/protocolActions.types';
import ParcelStack from '~/navigation/ParcelStack';
import { RouteSummary } from '~/types/routes.types';

export type ScheduleStackParamList = {
  SelectDateScreen: undefined;
  DailyScheduleScreen: { date: string; title: string };
  StopDetailScreen: { stop: Stop; title: string };
  SelectServiceScreen: { stop: Stop; title: string; completed: string[] };
  StopOverviewScreen: {
    title: string;
    task: TaskListItem;
    stop: Stop;
    stopList: Stop[];
  };
  MapNavigationScreen: {
    stop: Stop;
    currentRoute: RouteSummary | null;
    stopList: Stop[];
    lastCompletedStop?: Stop;
    isPickup: boolean;
    isDelivery: boolean;
    isService: boolean;
  };
  PhotoViewScreen: PhotoViewScreenProps['route']['params'];
  CollectSignature: {
    title: string;
    signatureURI: string;
    signatureFilePath: string;
    comments: string;
    onCompleteDropOff: (signature: string, comments: string) => void;
  };
  SelectParcelTypeScreen: { stop: Stop };
  ParcelInformationScreen: { stop: Stop };
  ParcelProofOfServiceScreen: { stop: Stop };
  ConfirmationScreen: {
    type: ConfirmationType;
    subType: ConfirmationSubType;
  };
  ProtocolEngineStack: {
    protocol: Protocol;
    scenario: ProtocolContextData;
    onSuccessCallback?: () => void;
  };
};

const Stack = createNativeStackNavigator<ScheduleStackParamList>();

function ScheduleStack() {
  const { syncUp, syncDown, status } = useSync();

  return (
    <SyncServiceProvider syncUp={syncUp} syncDown={syncDown} status={status}>
      <Stack.Navigator initialRouteName="SelectDateScreen">
        <Stack.Screen
          name="SelectDateScreen"
          component={SelectDateScreen}
          options={({ route }: any) => ({
            header: () => (
              <SubHeader
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.weekly_schedule
                }
                showBackArrow={false}
                showContactDispatch={false}
              />
            ),
          })}
        />

        <Stack.Screen
          name="DailyScheduleScreen"
          component={DailyScheduleScreen}
          options={{
            headerStyle: {
              backgroundColor: colors.darkBlue25,
            },
            header: ({ route }) => (
              <ThemedHeader title={route.params?.title} showBackArrow={true} />
            ),
          }}
        />
        <Stack.Screen
          name="StopDetailScreen"
          component={StopDetailScreen}
          options={({ route }: any) => ({
            header: () => (
              <SubHeader
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.stop_details
                }
              />
            ),
          })}
        />
        <Stack.Screen
          name="SelectServiceScreen"
          component={SelectServiceScreen}
          initialParams={{ completed: [] }}
          options={({ route }: any) => ({
            header: () => (
              <SubHeader
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.select_a_service
                }
              />
            ),
          })}
        />
        <Stack.Screen
          name="PhotoViewScreen"
          component={PhotoViewScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={route.params?.title}
                backgroundColor={colors.black}
              />
            ),
          })}
        />
        <Stack.Screen
          name="MapNavigationScreen"
          component={MapNavigationScreen}
          options={({ route }: any) => ({
            header: () => (
              <SubHeader
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.stop_details
                }
              />
            ),
          })}
        />

        <Stack.Screen
          name="CollectSignature"
          component={CollectSignature}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.collect_signature
                }
              />
            ),
          })}
        />

        <Stack.Screen
          name="StopOverviewScreen"
          component={StopOverviewScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.stop_overview
                }
                backgroundColor={colors.backgroundLight}
              />
            ),
            headerBackVisible: false,
          })}
        />

        <Stack.Screen
          name="CameraViewScreen"
          component={CameraViewScreen}
          options={() => ({
            headerShown: false,
          })}
        />

        <Stack.Screen
          name="ParcelStack"
          component={ParcelStack}
          options={() => ({
            headerShown: false,
          })}
        />

        <Stack.Screen
          name="ConfirmationScreen"
          component={ConfirmationScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                showBackArrow={false}
                showContactDispatch={false}
              />
            ),
          })}
        />

        <Stack.Screen
          name="ProtocolEngineStack"
          component={ProtocolEngineStack}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </SyncServiceProvider>
  );
}

export default ScheduleStack;
