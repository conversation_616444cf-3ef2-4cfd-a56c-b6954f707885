import React, { useMemo, useEffect, useState } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import colors from '~/styles/colors';
import { StyleSheet, TextStyle, View } from 'react-native';
import en from '~/localization/en';
import ErrorComponent from '~/components/error/ErrorComponent';
import { buttonText } from '~/styles/text';
import { useSyncLocation } from '~/hooks/dataSync/useSyncLocation';
import { useSyncService } from '~/services/sync/syncServiceContext';
import { SYNC_STATUS } from '~/hooks/useSync';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { useHeartbeatLocation } from '~/hooks/useHeartbeatLocation';
import { StopWithRelations } from '~/types/stops.types';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import ActiveScheduleList from '~/screens/schedule/ActiveScheduleListScreen';
import CompletedScheduleList from '~/screens/schedule/CompletedScheduleListScreen';
import { ProtocolService } from '~/services/protocol';
import { Protocol, ProtocolType } from '~/types/protocol.types';
import { ProtocolContextData } from '~/types/protocolActions.types';
import { DateTime } from 'luxon';
import { useIsOnline } from '~/hooks/useIsOnline';
import ToastMessage from '~/components/toast/ToastMessage';
import { paddingVertical16 } from '~/styles/spacing';
import { useRouteQueries } from '~/hooks/useRouteQueries';
import { useStopQueries } from '~/hooks/useStopQueries';
import { safeQueryFiltered } from '~/db/realm/utils/safeRealm';
import { checkProtocolVerificationExists } from '~/db/realm/operations/protocol-verification.operations';
import { isValidStop, sortStopsByTime } from '~/utils/stops';

const Tab = createMaterialTopTabNavigator();

interface Tasks {
  activeTasks: any[];
  completedTasks: any[];
  nextTask: any;
}

interface ProtocolTask {
  protocols: Protocol[];
  scenario: ProtocolContextData;
}

const TAB_NAMES = {
  ACTIVE: 'ActiveTab',
  COMPLETED: 'CompletedTab',
};

export interface DailyScheduleScreenProps {
  route: {
    params: {
      date: string;
    };
  };
}

const DailyScheduleScreen = ({ route }: DailyScheduleScreenProps) => {
  const { date } = route.params;
  const [nextTask, setNextTask] = useState<ProtocolTask | undefined>(undefined);
  const [isEndOfRouteSurveyPending, setIsEndOfRouteSurveyPending] =
    useState(false);
  const [heatProtocolTask, setHeatProtocolTask] = useState<
    ProtocolTask | undefined
  >(undefined);

  const [lastCompletedStop, setLastCompletedStop] = useState<
    StopWithRelations | undefined
  >(undefined);

  // send heartbeat location to to backend, if eligible
  const { handleTaskChange } = useHeartbeatLocation({
    date,
  });

  useSyncLocation();

  const { status, syncDown } = useSyncService();
  const { isOnline } = useIsOnline();

  const error = status === SYNC_STATUS.ERROR ? 'Sync error' : '';

  const { activeStopsForSelectedDate, completeStopsForSelectedDate } =
    useStopQueries(date);

  const { routesWithHighTemp, routeSummariesForSelectedDate } =
    useRouteQueries(date);

  const getFirstStopForRoute = async (routeId: string) => {
    try {
      if (!routeId || typeof routeId !== 'string') {
        throw new Error(`Invalid routeId provided: ${routeId}`);
      }
      const stops = await safeQueryFiltered<StopWithRelations>(
        StopSchema.name,
        'Summary__c = $0',
        [routeId],
      );

      if (!Array.isArray(stops)) {
        throw new Error(
          `Query returned invalid data type for routeId: ${routeId}`,
        );
      }

      if (stops.length === 0) {
        throw new Error(`No stops found for routeId: ${routeId}`);
      }

      const validStops = stops.filter(isValidStop);

      if (validStops.length === 0) {
        throw new Error(`No valid stops found for routeId: ${routeId}`);
      }

      const sortedStops = sortStopsByTime(validStops);
      return sortedStops[0];
    } catch (fetchError) {
      console.error('Error fetching first stop:', fetchError);
      return null;
    }
  };

  // Temperature protocol check
  useEffect(() => {
    const checkTemperatureProtocol = async () => {
      if (!activeStopsForSelectedDate.length) return;

      try {
        const routesWithFirstStops = await Promise.all(
          routesWithHighTemp.map(async currentRoute => ({
            route: currentRoute,
            firstStop: await getFirstStopForRoute(currentRoute.Id),
          })),
        );

        if (routesWithFirstStops.length === 0) {
          setHeatProtocolTask(undefined);
          return;
        }

        const firstRouteWithStop = routesWithFirstStops.find(
          routeWithStop => routeWithStop.firstStop !== null,
        );

        if (!firstRouteWithStop?.firstStop) {
          console.warn('No stop found for the route with high temperature');
          return;
        }

        const dailyScheduleId = firstRouteWithStop.firstStop.Daily_Schedule__c;

        if (!dailyScheduleId) {
          console.warn('No daily schedule ID found for the first stop');
          return;
        }

        // Check if a protocol verification record already exists for this daily schedule
        const protocolVerificationExists =
          await checkProtocolVerificationExists(dailyScheduleId);

        // Only show the protocol if no verification record exists
        if (protocolVerificationExists) {
          setHeatProtocolTask(undefined);
          return;
        }

        const result = await ProtocolService.checkProtocols(
          firstRouteWithStop.firstStop,
          ProtocolType.PRE_ROUTE_START,
        );

        if (result?.protocolData) {
          setHeatProtocolTask(result.protocolData);
        } else {
          setHeatProtocolTask(undefined);
        }
      } catch (protocolError) {
        setHeatProtocolTask(undefined);
        console.error('Error checking temperature protocol:', protocolError);
      }
    };

    checkTemperatureProtocol();
  }, [activeStopsForSelectedDate, routesWithHighTemp]);

  const {
    activeTasks,
    completedTasks,
    nextTask: _nextTask,
  } = useMemo<Tasks>(() => {
    if (
      (!activeStopsForSelectedDate ||
        activeStopsForSelectedDate.length === 0) &&
      (!completeStopsForSelectedDate ||
        completeStopsForSelectedDate.length === 0)
    ) {
      return { activeTasks: [], completedTasks: [], nextTask: undefined };
    }

    const activeStopList = [...activeStopsForSelectedDate];
    const completedStopList = [...completeStopsForSelectedDate];

    if (activeStopList.length > 0) {
      if (heatProtocolTask) {
        activeStopList.unshift(heatProtocolTask);
      }
      const currentStopData = isEndOfRouteSurveyPending
        ? undefined
        : activeStopList.shift();

      return {
        activeTasks: [
          ...Array.from(activeStopList),
          ...Array.from(routeSummariesForSelectedDate),
        ],
        completedTasks: Array.from(completedStopList),
        nextTask: currentStopData,
      };
    } else if (routeSummariesForSelectedDate.length > 0) {
      return {
        activeTasks: Array.from(routeSummariesForSelectedDate),
        completedTasks: Array.from(completedStopList),
        nextTask: undefined,
      };
    }

    return {
      activeTasks: [],
      completedTasks: Array.from(completedStopList),
      nextTask: undefined,
    };
  }, [
    activeStopsForSelectedDate,
    completeStopsForSelectedDate,
    routeSummariesForSelectedDate,
    isEndOfRouteSurveyPending,
    heatProtocolTask,
  ]);

  useEffect(() => {
    if (nextTask?.Id) {
      handleTaskChange();
    }
  }, [nextTask?.Id, handleTaskChange]);

  const memoizedActiveTasksCount = useMemo(() => {
    const taskCount = activeTasks.length;
    const effectiveNextTask = nextTask ?? _nextTask;

    if (effectiveNextTask) {
      if (taskCount > 0) {
        return taskCount + 1;
      }
      return 1;
    } else if (taskCount > 0) {
      return taskCount;
    }

    return '';
  }, [activeTasks.length, nextTask, _nextTask]);

  const getProtocol = async (
    recentlyCompletedStop: StopWithRelations,
  ): Promise<ProtocolTask | undefined> => {
    try {
      const result = await ProtocolService.checkEndOfRouteProtocols(
        recentlyCompletedStop,
      );
      return result?.protocolData ?? undefined;
    } catch (e) {
      console.error('Failed to fetch next protocol:', e);
      return undefined;
    }
  };

  useEffect(() => {
    const updateNextTask = async () => {
      if (!completedTasks.length) {
        setNextTask(undefined);
        return;
      }

      const recentlyCompletedStop = completedTasks.reduce((acc, stop) => {
        if (
          !acc ||
          DateTime.fromISO(stop.Completed_Time__c).toMillis() >
            DateTime.fromISO(acc.Completed_Time__c).toMillis()
        ) {
          return stop;
        }
        return acc;
      }, null);

      if (!recentlyCompletedStop) {
        setNextTask(undefined);
        setLastCompletedStop(undefined);
        return;
      }
      setLastCompletedStop(recentlyCompletedStop);
    };

    updateNextTask();
  }, [completedTasks]);

  useEffect(() => {
    const triggerProtocol = async (): Promise<ProtocolTask | undefined> => {
      if (!lastCompletedStop) {
        return;
      }
      const nextProtocol = await getProtocol(lastCompletedStop);

      if (nextProtocol) {
        setNextTask(nextProtocol);
        setIsEndOfRouteSurveyPending(true);
      }
    };
    triggerProtocol();
  }, [lastCompletedStop]);

  const memoizedCompletedTasksCount = useMemo(() => {
    return completedTasks.length > 0 ? completedTasks.length : '';
  }, [completedTasks]);

  const offlineBanner = !isOnline ? (
    <View style={paddingVertical16}>
      <ToastMessage
        variant="banner"
        type="warning"
        title="You’re currently offline"
        message="Please check your internet connection to get back online. Data will sync when back online."
        testID="ToastMessage.Banner.Offline"
      />
    </View>
  ) : null;

  return (
    <ScreenWrapper>
      {error &&
      (activeStopsForSelectedDate === undefined ||
        completeStopsForSelectedDate === undefined) ? (
        <ErrorComponent errorText={error} />
      ) : (
        <Tab.Navigator
          screenOptions={{
            tabBarAllowFontScaling: true,
            tabBarActiveTintColor: colors.darkBlue500,
            tabBarInactiveTintColor: colors.grey900,
            swipeEnabled: false,
            tabBarLabelStyle: styles.tabBarLabelStyle,
            tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
            tabBarStyle: styles.tabBarStyle,
          }}>
          <Tab.Screen
            name={TAB_NAMES.ACTIVE}
            options={{
              tabBarLabel: `${memoizedActiveTasksCount} ${en.active}`.trim(),
            }}>
            {() => (
              <ActiveScheduleList
                tasks={activeTasks}
                nextTask={nextTask ?? _nextTask}
                onRefresh={syncDown}
                date={date}
                headerComponent={offlineBanner}
              />
            )}
          </Tab.Screen>
          <Tab.Screen
            name={TAB_NAMES.COMPLETED}
            options={{
              tabBarLabel:
                `${memoizedCompletedTasksCount} ${en.completed}`.trim(),
            }}>
            {() => (
              <CompletedScheduleList
                tasks={completedTasks}
                onRefresh={syncDown}
                date={date}
                headerComponent={offlineBanner}
              />
            )}
          </Tab.Screen>
        </Tab.Navigator>
      )}
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 10,
  },
  tabBarLabelStyle: {
    ...buttonText,
    textTransform: 'none',
  } as TextStyle,
  tabBarIndicatorStyle: {
    backgroundColor: colors.darkBlue500,
    height: 3,
  },
  tabBarStyle: {
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray, // Inactive underline color
    elevation: 0,
    backgroundColor: colors.backgroundLight,
  },
});

export default DailyScheduleScreen;
