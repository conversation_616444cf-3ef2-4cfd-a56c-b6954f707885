import cvReadyPromise from '@techstark/opencv-js';

class BlurDetectionService {
  constructor() {
    this.cv = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (!this.isInitialized) {
      this.cv = await cvReadyPromise;
      this.isInitialized = true;
      console.log('OpenCV.js is ready!');
    }
    return this.cv;
  }

  /**
   * Calculate Laplacian variance for blur detection
   * @param {ImageData} imageData - ImageData object from canvas or camera
   * @returns {number} - Laplacian variance (higher values = less blur)
   */
  async getLaplacianVariance(imageData) {
    await this.initialize();
    
    try {
      // Convert ImageData to OpenCV Mat
      const cvImage = this.cv.matFromImageData(imageData);
      const grayImage = new this.cv.Mat();
      const laplacianMat = new this.cv.Mat();

      // Convert to grayscale
      this.cv.cvtColor(cvImage, grayImage, this.cv.COLOR_RGBA2GRAY, 0);
      
      // Apply Laplacian filter
      this.cv.Laplacian(grayImage, laplacianMat, this.cv.CV_64F);

      // Calculate variance using standard deviation method
      const mean = new this.cv.Mat(1, 4, this.cv.CV_64F);
      const standardDeviationMat = new this.cv.Mat(1, 4, this.cv.CV_64F);
      
      this.cv.meanStdDev(laplacianMat, mean, standardDeviationMat);
      
      const standardDeviation = standardDeviationMat.doubleAt(0, 0);
      const laplacianVariance = standardDeviation * standardDeviation;

      // Clean up memory
      cvImage.delete();
      grayImage.delete();
      laplacianMat.delete();
      mean.delete();
      standardDeviationMat.delete();

      return laplacianVariance;
    } catch (error) {
      console.error('Error calculating Laplacian variance:', error);
      return null;
    }
  }

  /**
   * Alternative method using manual variance calculation
   * @param {ImageData} imageData - ImageData object from canvas or camera
   * @returns {number} - Laplacian variance
   */
  async getLaplacianVarianceManual(imageData) {
    await this.initialize();
    
    try {
      const cvImage = this.cv.matFromImageData(imageData);
      const grayImage = new this.cv.Mat();
      const laplacianMat = new this.cv.Mat();

      this.cv.cvtColor(cvImage, grayImage, this.cv.COLOR_RGBA2GRAY, 0);
      this.cv.Laplacian(grayImage, laplacianMat, this.cv.CV_8U);

      // Manual variance calculation
      const data = laplacianMat.data;
      const variance = this.calculateVariance(Array.from(data));

      // Clean up memory
      cvImage.delete();
      grayImage.delete();
      laplacianMat.delete();

      return variance;
    } catch (error) {
      console.error('Error calculating Laplacian variance (manual):', error);
      return null;
    }
  }

  /**
   * Helper function to calculate variance manually
   * @param {Array} array - Array of numbers
   * @returns {number} - Variance
   */
  calculateVariance(array) {
    const mean = array.reduce((acc, x) => acc + x, 0) / array.length;
    return array.reduce((acc, el) => acc + Math.pow(el - mean, 2), 0) / array.length;
  }

  /**
   * Determine if image is blurry based on threshold
   * @param {ImageData} imageData - ImageData object
   * @param {number} threshold - Blur threshold (default: 100)
   * @returns {boolean} - true if image is blurry
   */
  async isImageBlurry(imageData, threshold = 100) {
    const variance = await this.getLaplacianVariance(imageData);
    if (variance === null) return null;
    
    return variance < threshold;
  }

  /**
   * Get blur score with interpretation
   * @param {ImageData} imageData - ImageData object
   * @returns {Object} - Object with variance and interpretation
   */
  async getBlurScore(imageData) {
    const variance = await this.getLaplacianVariance(imageData);
    if (variance === null) return null;

    let interpretation;
    if (variance < 50) {
      interpretation = 'Very Blurry';
    } else if (variance < 100) {
      interpretation = 'Blurry';
    } else if (variance < 200) {
      interpretation = 'Slightly Blurry';
    } else {
      interpretation = 'Sharp';
    }

    return {
      variance,
      interpretation,
      isBlurry: variance < 100
    };
  }
}

// Export singleton instance
export default new BlurDetectionService();
