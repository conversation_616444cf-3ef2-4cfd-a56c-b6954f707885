import cvReadyPromise from '@techstark/opencv-js';
import RNFS from 'react-native-fs';

class ImageAnalysisService {
  constructor() {
    this.cv = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (!this.isInitialized) {
      this.cv = await cvReadyPromise;
      this.isInitialized = true;
      console.log('OpenCV.js is ready for image analysis!');
    }
    return this.cv;
  }

  // ========== IMAGE FORMATTING & PREPARATION ==========

  /**
   * Convert various image formats to ImageData for analysis
   * @param {string|Object} imageInput - Can be file path, base64, URI, or camera data
   * @param {string} inputType - 'path', 'base64', 'uri', 'camera'
   * @returns {Promise<ImageData>} - Standardized ImageData object
   */
  async prepareImageForAnalysis(imageInput, inputType = 'path') {
    try {
      switch (inputType) {
        case 'path':
          return await this.filePathToImageData(imageInput);
        case 'base64':
          return await this.base64ToImageData(imageInput);
        case 'uri':
          return await this.uriToImageData(imageInput);
        case 'camera':
          return await this.cameraDataToImageData(imageInput);
        default:
          throw new Error(`Unsupported input type: ${inputType}`);
      }
    } catch (error) {
      console.error('Error preparing image for analysis:', error);
      throw error;
    }
  }

  /**
   * Convert file path to ImageData
   * @param {string} filePath - Path to image file
   * @returns {Promise<ImageData>} - ImageData object
   */
  async filePathToImageData(filePath) {
    const base64Image = await RNFS.readFile(filePath, 'base64');
    return this.base64ToImageData(base64Image);
  }

  /**
   * Convert base64 string to ImageData
   * @param {string} base64 - Base64 encoded image
   * @returns {Promise<ImageData>} - ImageData object
   */
  async base64ToImageData(base64) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);

        const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
        if (imageData) {
          resolve(imageData);
        } else {
          reject(new Error('Failed to get image data from base64'));
        }
      };

      img.onerror = () => reject(new Error('Failed to load base64 image'));
      img.src = base64.startsWith('data:') ? base64 : `data:image/jpeg;base64,${base64}`;
    });
  }

  /**
   * Convert URI to ImageData
   * @param {string} uri - Image URI
   * @returns {Promise<ImageData>} - ImageData object
   */
  async uriToImageData(uri) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new window.Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);

        const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
        if (imageData) {
          resolve(imageData);
        } else {
          reject(new Error('Failed to get image data from URI'));
        }
      };

      img.onerror = () => reject(new Error('Failed to load image from URI'));
      img.src = uri;
    });
  }

  /**
   * Convert camera data to ImageData (placeholder for camera integration)
   * @param {Object} cameraData - Camera frame or photo data
   * @returns {Promise<ImageData>} - ImageData object
   */
  async cameraDataToImageData(cameraData) {
    // This will depend on your camera implementation
    // For now, assume it has a path property
    if (cameraData.path) {
      return this.filePathToImageData(cameraData.path);
    }
    throw new Error('Camera data format not supported yet');
  }

  // ========== BLUR DETECTION ANALYSIS ==========

  /**
   * Calculate Laplacian variance for blur detection
   * @param {string|Object} imageInput - Image input (path, base64, etc.)
   * @param {string} inputType - Type of input ('path', 'base64', 'uri', 'camera')
   * @returns {Promise<number>} - Laplacian variance (higher values = less blur)
   */
  async calculateBlurVariance(imageInput, inputType = 'path') {
    await this.initialize();

    try {
      const imageData = await this.prepareImageForAnalysis(imageInput, inputType);
      return await this.getLaplacianVarianceFromImageData(imageData);
    } catch (error) {
      console.error('Error calculating blur variance:', error);
      return null;
    }
  }

  /**
   * Calculate Laplacian variance from ImageData
   * @param {ImageData} imageData - ImageData object
   * @returns {Promise<number>} - Laplacian variance
   */
  async getLaplacianVarianceFromImageData(imageData) {
    try {
      // Convert ImageData to OpenCV Mat
      const cvImage = this.cv.matFromImageData(imageData);
      const grayImage = new this.cv.Mat();
      const laplacianMat = new this.cv.Mat();

      // Convert to grayscale
      this.cv.cvtColor(cvImage, grayImage, this.cv.COLOR_RGBA2GRAY, 0);

      // Apply Laplacian filter
      this.cv.Laplacian(grayImage, laplacianMat, this.cv.CV_64F);

      // Calculate variance using standard deviation method
      const mean = new this.cv.Mat(1, 4, this.cv.CV_64F);
      const standardDeviationMat = new this.cv.Mat(1, 4, this.cv.CV_64F);

      this.cv.meanStdDev(laplacianMat, mean, standardDeviationMat);

      const standardDeviation = standardDeviationMat.doubleAt(0, 0);
      const laplacianVariance = standardDeviation * standardDeviation;

      // Clean up memory
      cvImage.delete();
      grayImage.delete();
      laplacianMat.delete();
      mean.delete();
      standardDeviationMat.delete();

      return laplacianVariance;
    } catch (error) {
      console.error('Error calculating Laplacian variance from ImageData:', error);
      return null;
    }
  }

  /**
   * Analyze image blur with detailed results
   * @param {string|Object} imageInput - Image input
   * @param {string} inputType - Type of input
   * @param {number} threshold - Blur threshold (default: 100)
   * @returns {Promise<Object>} - Detailed blur analysis
   */
  async analyzeBlur(imageInput, inputType = 'path', threshold = 100) {
    try {
      const variance = await this.calculateBlurVariance(imageInput, inputType);
      if (variance === null) return null;

      let interpretation;
      if (variance < 50) {
        interpretation = 'Very Blurry';
      } else if (variance < 100) {
        interpretation = 'Blurry';
      } else if (variance < 200) {
        interpretation = 'Slightly Blurry';
      } else {
        interpretation = 'Sharp';
      }

      return {
        variance,
        interpretation,
        isBlurry: variance < threshold,
        threshold,
        analysisType: 'blur_detection'
      };
    } catch (error) {
      console.error('Error analyzing blur:', error);
      return null;
    }
  }

  /**
   * Quick blur check - returns boolean
   * @param {string|Object} imageInput - Image input
   * @param {string} inputType - Type of input
   * @param {number} threshold - Blur threshold
   * @returns {Promise<boolean>} - true if image is blurry
   */
  async isImageBlurry(imageInput, inputType = 'path', threshold = 100) {
    const variance = await this.calculateBlurVariance(imageInput, inputType);
    return variance !== null ? variance < threshold : null;
  }

  // ========== FUTURE ANALYSIS METHODS ==========
  // Placeholder methods for future image analysis features

  /**
   * Analyze image brightness (placeholder for future implementation)
   * @param {string|Object} imageInput - Image input
   * @param {string} inputType - Type of input
   * @returns {Promise<Object>} - Brightness analysis
   */
  async analyzeBrightness(imageInput, inputType = 'path') {
    // TODO: Implement brightness analysis
    console.log('Brightness analysis not yet implemented');
    return null;
  }

  /**
   * Analyze image contrast (placeholder for future implementation)
   * @param {string|Object} imageInput - Image input
   * @param {string} inputType - Type of input
   * @returns {Promise<Object>} - Contrast analysis
   */
  async analyzeContrast(imageInput, inputType = 'path') {
    // TODO: Implement contrast analysis
    console.log('Contrast analysis not yet implemented');
    return null;
  }

  /**
   * Comprehensive image quality analysis
   * @param {string|Object} imageInput - Image input
   * @param {string} inputType - Type of input
   * @returns {Promise<Object>} - Complete image quality analysis
   */
  async analyzeImageQuality(imageInput, inputType = 'path') {
    try {
      const blurAnalysis = await this.analyzeBlur(imageInput, inputType);
      // const brightnessAnalysis = await this.analyzeBrightness(imageInput, inputType);
      // const contrastAnalysis = await this.analyzeContrast(imageInput, inputType);

      return {
        blur: blurAnalysis,
        // brightness: brightnessAnalysis,
        // contrast: contrastAnalysis,
        overall: {
          quality: blurAnalysis?.isBlurry ? 'Poor' : 'Good',
          recommendation: blurAnalysis?.isBlurry ? 'Retake photo - image is blurry' : 'Image quality is acceptable'
        }
      };
    } catch (error) {
      console.error('Error analyzing image quality:', error);
      return null;
    }
  }
}

// Export singleton instance
export default new ImageAnalysisService();
