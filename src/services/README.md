# ImageAnalysisService

A comprehensive service for analyzing images in React Native using OpenCV.js. This service handles image format conversion and provides various analysis capabilities, with blur detection as the primary feature.

## Features

- **Image Format Handling**: Automatically converts various image formats (file paths, base64, URIs, camera data) to the format needed for analysis
- **Blur Detection**: Implements Laplacian variance calculation for blur detection
- **Extensible Architecture**: Designed to easily add more analysis types (brightness, contrast, etc.)
- **Memory Management**: Properly handles OpenCV Mat object cleanup to prevent memory leaks

## Installation

First, install the required dependency:

```bash
npm install @techstark/opencv-js
```

## Basic Usage

### Import the Service

```javascript
import ImageAnalysisService from '../services/ImageAnalysisService';
```

### Analyze Image Blur

```javascript
// From file path (most common for camera photos)
const result = await ImageAnalysisService.analyzeBlur('/path/to/image.jpg', 'path');

// From base64 string
const result = await ImageAnalysisService.analyzeBlur(base64String, 'base64');

// From URI
const result = await ImageAnalysisService.analyzeBlur('file://path/to/image.jpg', 'uri');
```

### Result Format

```javascript
{
  variance: 150.25,           // Laplacian variance (higher = sharper)
  interpretation: 'Sharp',    // Human-readable interpretation
  isBlurry: false,           // Boolean blur status
  threshold: 100,            // Threshold used for blur detection
  analysisType: 'blur_detection'
}
```

### Comprehensive Image Quality Analysis

```javascript
const qualityResult = await ImageAnalysisService.analyzeImageQuality('/path/to/image.jpg', 'path');

// Returns:
{
  blur: {
    variance: 150.25,
    interpretation: 'Sharp',
    isBlurry: false,
    threshold: 100,
    analysisType: 'blur_detection'
  },
  overall: {
    quality: 'Good',
    recommendation: 'Image quality is acceptable'
  }
}
```

## Integration with Camera

### With react-native-vision-camera

```javascript
import { Camera } from 'react-native-vision-camera';
import ImageAnalysisService from '../services/ImageAnalysisService';

const takePhotoAndAnalyze = async () => {
  const photo = await camera.current.takePhoto();
  
  // Analyze the photo for blur
  const blurResult = await ImageAnalysisService.analyzeBlur(photo.path, 'path');
  
  if (blurResult.isBlurry) {
    Alert.alert('Blurry Photo', 'Please retake the photo - it appears to be blurry');
  } else {
    // Photo is sharp, proceed with saving/uploading
    console.log('Photo is sharp!', blurResult);
  }
};
```

### With File System

```javascript
import RNFS from 'react-native-fs';
import ImageAnalysisService from '../services/ImageAnalysisService';

const analyzeImageFromFile = async (filePath) => {
  try {
    const result = await ImageAnalysisService.analyzeBlur(filePath, 'path');
    return result;
  } catch (error) {
    console.error('Analysis failed:', error);
    return null;
  }
};
```

## Blur Detection Thresholds

The service uses these default interpretations:

- **< 50**: Very Blurry
- **50-100**: Blurry  
- **100-200**: Slightly Blurry
- **> 200**: Sharp

You can customize the threshold:

```javascript
const isBlurry = await ImageAnalysisService.isImageBlurry('/path/to/image.jpg', 'path', 150);
```

## Future Extensions

The service is designed to be easily extended with additional analysis types:

```javascript
// These methods are placeholders for future implementation
await ImageAnalysisService.analyzeBrightness(imagePath, 'path');
await ImageAnalysisService.analyzeContrast(imagePath, 'path');
```

## Error Handling

The service returns `null` for failed analyses. Always check for null results:

```javascript
const result = await ImageAnalysisService.analyzeBlur(imagePath, 'path');
if (result === null) {
  console.error('Analysis failed');
  return;
}

// Use result safely
console.log('Blur variance:', result.variance);
```

## Memory Management

The service automatically handles OpenCV Mat object cleanup, but make sure to:

1. Not hold references to analysis results longer than necessary
2. Call analysis methods in try-catch blocks
3. Handle null results appropriately

## Performance Notes

- Image analysis is CPU-intensive and should be done on background threads when possible
- Larger images take longer to analyze - consider resizing very large images
- The service initializes OpenCV.js on first use, which may take a moment
