import ImageAnalysisService from '../services/ImageAnalysisService';

/**
 * Test suite for ImageAnalysisService
 * Run this to validate blur detection is working correctly
 */
class ImageAnalysisTest {
  
  static async runAllTests() {
    console.log('🧪 Starting ImageAnalysisService Tests...\n');
    
    try {
      await this.testServiceInitialization();
      await this.testBlurDetectionWithSampleImages();
      await this.testDifferentImageFormats();
      await this.testErrorHandling();
      
      console.log('✅ All tests completed successfully!');
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  static async testServiceInitialization() {
    console.log('📋 Test 1: Service Initialization');
    
    try {
      // Test that the service can initialize OpenCV
      await ImageAnalysisService.initialize();
      console.log('✅ Service initialized successfully');
    } catch (error) {
      console.error('❌ Service initialization failed:', error);
      throw error;
    }
    
    console.log('');
  }

  static async testBlurDetectionWithSampleImages() {
    console.log('📋 Test 2: Blur Detection with Sample Images');
    
    // You'll need to add actual image paths here
    const testImages = [
      {
        path: '/path/to/sharp/image.jpg',
        expectedBlurry: false,
        description: 'Sharp test image'
      },
      {
        path: '/path/to/blurry/image.jpg', 
        expectedBlurry: true,
        description: 'Blurry test image'
      },
      {
        path: '/path/to/slightly/blurry/image.jpg',
        expectedBlurry: true, // or false depending on your threshold
        description: 'Slightly blurry test image'
      }
    ];

    for (const testImage of testImages) {
      try {
        console.log(`Testing: ${testImage.description}`);
        
        const result = await ImageAnalysisService.analyzeBlur(testImage.path, 'path');
        
        if (result) {
          console.log(`  📊 Variance: ${result.variance.toFixed(2)}`);
          console.log(`  📝 Interpretation: ${result.interpretation}`);
          console.log(`  🎯 Is Blurry: ${result.isBlurry}`);
          console.log(`  ✅ Expected Blurry: ${testImage.expectedBlurry}`);
          
          // Check if result matches expectation
          if (result.isBlurry === testImage.expectedBlurry) {
            console.log('  ✅ Result matches expectation');
          } else {
            console.log('  ⚠️  Result differs from expectation - check threshold or image');
          }
        } else {
          console.log('  ❌ Analysis returned null');
        }
        
        console.log('');
      } catch (error) {
        console.error(`  ❌ Error testing ${testImage.description}:`, error);
      }
    }
  }

  static async testDifferentImageFormats() {
    console.log('📋 Test 3: Different Image Formats');
    
    // Test with a sample image path (you'll need to provide a real path)
    const sampleImagePath = '/path/to/test/image.jpg';
    
    try {
      // Test path format
      console.log('Testing path format...');
      const pathResult = await ImageAnalysisService.analyzeBlur(sampleImagePath, 'path');
      console.log(`  Path result: ${pathResult ? 'Success' : 'Failed'}`);
      
      // Test base64 format (you'd need to convert an image to base64)
      // console.log('Testing base64 format...');
      // const base64Result = await ImageAnalysisService.analyzeBlur(base64String, 'base64');
      // console.log(`  Base64 result: ${base64Result ? 'Success' : 'Failed'}`);
      
    } catch (error) {
      console.error('  ❌ Format testing failed:', error);
    }
    
    console.log('');
  }

  static async testErrorHandling() {
    console.log('📋 Test 4: Error Handling');
    
    try {
      // Test with non-existent file
      console.log('Testing non-existent file...');
      const result = await ImageAnalysisService.analyzeBlur('/non/existent/path.jpg', 'path');
      console.log(`  Non-existent file result: ${result === null ? 'Correctly returned null' : 'Unexpected result'}`);
      
      // Test with invalid input type
      console.log('Testing invalid input type...');
      const invalidResult = await ImageAnalysisService.analyzeBlur('test', 'invalid_type');
      console.log(`  Invalid type result: ${invalidResult === null ? 'Correctly returned null' : 'Unexpected result'}`);
      
    } catch (error) {
      console.log('  ✅ Errors handled gracefully:', error.message);
    }
    
    console.log('');
  }

  // Helper method to test with camera-like data
  static async testCameraIntegration(photoPath) {
    console.log('📋 Camera Integration Test');
    console.log(`Testing photo: ${photoPath}`);
    
    try {
      const startTime = Date.now();
      const result = await ImageAnalysisService.analyzeBlur(photoPath, 'path');
      const endTime = Date.now();
      
      if (result) {
        console.log(`  📊 Analysis completed in ${endTime - startTime}ms`);
        console.log(`  📊 Variance: ${result.variance.toFixed(2)}`);
        console.log(`  📝 Interpretation: ${result.interpretation}`);
        console.log(`  🎯 Recommendation: ${result.isBlurry ? 'Retake photo' : 'Photo is acceptable'}`);
        
        return result;
      } else {
        console.log('  ❌ Analysis failed');
        return null;
      }
    } catch (error) {
      console.error('  ❌ Camera integration test failed:', error);
      return null;
    }
  }

  // Performance test
  static async testPerformance(imagePath, iterations = 5) {
    console.log(`📋 Performance Test (${iterations} iterations)`);
    
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await ImageAnalysisService.analyzeBlur(imagePath, 'path');
      const endTime = Date.now();
      times.push(endTime - startTime);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`  📊 Average time: ${avgTime.toFixed(2)}ms`);
    console.log(`  📊 Min time: ${minTime}ms`);
    console.log(`  📊 Max time: ${maxTime}ms`);
    
    return { avgTime, minTime, maxTime };
  }
}

export default ImageAnalysisTest;

// Example usage:
// ImageAnalysisTest.runAllTests();
// ImageAnalysisTest.testCameraIntegration('/path/to/camera/photo.jpg');
// ImageAnalysisTest.testPerformance('/path/to/test/image.jpg');
