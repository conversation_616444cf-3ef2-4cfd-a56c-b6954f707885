import ImageAnalysisService from '../services/ImageAnalysisService';

/**
 * Quick test function - just call this with a real image path
 */
export const quickTest = async (imagePath) => {
  console.log(`🧪 Quick testing image: ${imagePath}`);
  
  try {
    const startTime = Date.now();
    const result = await ImageAnalysisService.analyzeBlur(imagePath, 'path');
    const endTime = Date.now();
    
    if (result) {
      console.log('✅ Analysis successful!');
      console.log(`📊 Variance: ${result.variance.toFixed(2)}`);
      console.log(`📝 Interpretation: ${result.interpretation}`);
      console.log(`🎯 Is Blurry: ${result.isBlurry}`);
      console.log(`⏱️  Analysis Time: ${endTime - startTime}ms`);
      
      // Recommendation
      if (result.isBlurry) {
        console.log('🔄 Recommendation: Retake photo - appears blurry');
      } else {
        console.log('✅ Recommendation: Photo quality is acceptable');
      }
      
      return result;
    } else {
      console.log('❌ Analysis failed - check image path');
      return null;
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
    return null;
  }
};

// Example usage:
// import { quickTest } from './tests/QuickTest';
// quickTest('/path/to/your/test/image.jpg');
