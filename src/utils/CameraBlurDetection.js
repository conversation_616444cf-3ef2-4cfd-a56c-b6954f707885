import ImageAnalysisService from '../services/ImageAnalysisService';

/**
 * Utility functions for camera blur detection
 * This is a convenience wrapper around ImageAnalysisService for camera-specific use cases
 */
export class CameraBlurDetection {

  /**
   * Analyze blur from a camera photo
   * @param {string} photoPath - Path to the photo file
   * @returns {Promise<Object>} - Blur analysis result
   */
  static async analyzePhotoBlur(photoPath) {
    try {
      // Use the ImageAnalysisService which handles all the image conversion internally
      const result = await ImageAnalysisService.analyzeBlur(photoPath, 'path');
      return result;
    } catch (error) {
      console.error('Error analyzing photo blur:', error);
      throw error;
    }
  }

  /**
   * Quick check if a camera photo is blurry
   * @param {string} photoPath - Path to the photo file
   * @param {number} threshold - Blur threshold (default: 100)
   * @returns {Promise<boolean>} - true if image is blurry
   */
  static async isPhotoBlurry(photoPath, threshold = 100) {
    try {
      const result = await ImageAnalysisService.isImageBlurry(photoPath, 'path', threshold);
      return result;
    } catch (error) {
      console.error('Error checking photo blur:', error);
      return null;
    }
  }

  /**
   * Analyze comprehensive image quality from camera photo
   * @param {string} photoPath - Path to the photo file
   * @returns {Promise<Object>} - Complete image quality analysis
   */
  static async analyzePhotoQuality(photoPath) {
    try {
      const result = await ImageAnalysisService.analyzeImageQuality(photoPath, 'path');
      return result;
    } catch (error) {
      console.error('Error analyzing photo quality:', error);
      throw error;
    }
  }

  /**
   * Get blur variance value only
   * @param {string} photoPath - Path to the photo file
   * @returns {Promise<number>} - Blur variance (higher = sharper)
   */
  static async getPhotoBlurVariance(photoPath) {
    try {
      const variance = await ImageAnalysisService.calculateBlurVariance(photoPath, 'path');
      return variance;
    } catch (error) {
      console.error('Error getting photo blur variance:', error);
      return null;
    }
  }
}

/**
 * Hook for using blur detection in React Native components
 */
export const useBlurDetection = () => {
  const analyzeImage = async (imagePath) => {
    try {
      const result = await CameraBlurDetection.analyzePhotoBlur(imagePath);
      return result;
    } catch (error) {
      console.error('Blur detection error:', error);
      return null;
    }
  };

  const isImageSharp = async (imagePath, threshold = 100) => {
    const result = await analyzeImage(imagePath);
    return result ? result.variance >= threshold : null;
  };

  return {
    analyzeImage,
    isImageSharp,
  };
};
