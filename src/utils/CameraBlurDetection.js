import BlurDetectionService from '../services/BlurDetectionService';
import RNFS from 'react-native-fs';

/**
 * Utility functions for camera blur detection
 */
export class CameraBlurDetection {
  
  /**
   * Analyze blur from a camera photo
   * @param {string} photoPath - Path to the photo file
   * @returns {Promise<Object>} - Blur analysis result
   */
  static async analyzePhotoBlur(photoPath) {
    try {
      // Read the image file as base64
      const base64Image = await RNFS.readFile(photoPath, 'base64');
      
      // Convert to ImageData
      const imageData = await this.base64ToImageData(base64Image);
      
      // Analyze blur
      const result = await BlurDetectionService.getBlurScore(imageData);
      
      return result;
    } catch (error) {
      console.error('Error analyzing photo blur:', error);
      throw error;
    }
  }

  /**
   * Convert base64 image to ImageData
   * @param {string} base64 - Base64 encoded image
   * @returns {Promise<ImageData>} - ImageData object
   */
  static base64ToImageData(base64) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
        if (imageData) {
          resolve(imageData);
        } else {
          reject(new Error('Failed to get image data'));
        }
      };
      
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = `data:image/jpeg;base64,${base64}`;
    });
  }

  /**
   * Real-time blur detection for camera preview (if needed)
   * @param {Object} frame - Camera frame data
   * @returns {Promise<number>} - Blur variance
   */
  static async analyzeFrameBlur(frame) {
    try {
      // This would depend on your camera implementation
      // You'd need to convert the camera frame to ImageData
      const imageData = await this.frameToImageData(frame);
      const variance = await BlurDetectionService.getLaplacianVariance(imageData);
      return variance;
    } catch (error) {
      console.error('Error analyzing frame blur:', error);
      return null;
    }
  }

  /**
   * Convert camera frame to ImageData (implementation depends on camera library)
   * @param {Object} frame - Camera frame
   * @returns {Promise<ImageData>} - ImageData object
   */
  static frameToImageData(frame) {
    // This is a placeholder - actual implementation would depend on
    // how your camera library provides frame data
    throw new Error('frameToImageData not implemented - depends on camera library');
  }
}

/**
 * Hook for using blur detection in React Native components
 */
export const useBlurDetection = () => {
  const analyzeImage = async (imagePath) => {
    try {
      const result = await CameraBlurDetection.analyzePhotoBlur(imagePath);
      return result;
    } catch (error) {
      console.error('Blur detection error:', error);
      return null;
    }
  };

  const isImageSharp = async (imagePath, threshold = 100) => {
    const result = await analyzeImage(imagePath);
    return result ? result.variance >= threshold : null;
  };

  return {
    analyzeImage,
    isImageSharp,
  };
};
