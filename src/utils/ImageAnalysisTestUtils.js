import ImageAnalysisService from '../services/ImageAnalysisService';
import { CameraBlurDetection } from './CameraBlurDetection';

/**
 * Utility functions for testing ImageAnalysisService
 * These can be used in development/testing to validate the service
 */
export class ImageAnalysisTestUtils {
  
  /**
   * Quick test function for a single image
   * @param {string} imagePath - Path to test image
   * @returns {Promise<Object>} - Test result with timing info
   */
  static async quickTest(imagePath) {
    console.log(`🧪 Testing image: ${imagePath}`);
    
    try {
      const startTime = Date.now();
      const result = await ImageAnalysisService.analyzeBlur(imagePath, 'path');
      const endTime = Date.now();
      
      if (result) {
        const testResult = {
          success: true,
          path: imagePath,
          variance: result.variance,
          interpretation: result.interpretation,
          isBlurry: result.isBlurry,
          analysisTime: endTime - startTime,
          timestamp: new Date().toISOString(),
        };
        
        console.log('✅ Analysis successful!');
        console.log(`📊 Variance: ${result.variance.toFixed(2)}`);
        console.log(`📝 Interpretation: ${result.interpretation}`);
        console.log(`🎯 Is Blurry: ${result.isBlurry}`);
        console.log(`⏱️  Analysis Time: ${endTime - startTime}ms`);
        
        return testResult;
      } else {
        console.log('❌ Analysis failed - check image path');
        return {
          success: false,
          path: imagePath,
          error: 'Analysis returned null',
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      return {
        success: false,
        path: imagePath,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Test multiple images and return summary
   * @param {string[]} imagePaths - Array of image paths to test
   * @returns {Promise<Object>} - Summary of all tests
   */
  static async batchTest(imagePaths) {
    console.log(`🧪 Running batch test on ${imagePaths.length} images...`);
    
    const results = [];
    const startTime = Date.now();
    
    for (const path of imagePaths) {
      const result = await this.quickTest(path);
      results.push(result);
    }
    
    const endTime = Date.now();
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    const summary = {
      totalTests: results.length,
      successful: successful.length,
      failed: failed.length,
      totalTime: endTime - startTime,
      averageTime: successful.length > 0 
        ? successful.reduce((sum, r) => sum + r.analysisTime, 0) / successful.length 
        : 0,
      results,
      timestamp: new Date().toISOString(),
    };
    
    console.log(`📊 Batch Test Summary:`);
    console.log(`   Total: ${summary.totalTests}`);
    console.log(`   Successful: ${summary.successful}`);
    console.log(`   Failed: ${summary.failed}`);
    console.log(`   Total Time: ${summary.totalTime}ms`);
    console.log(`   Average Analysis Time: ${summary.averageTime.toFixed(2)}ms`);
    
    return summary;
  }

  /**
   * Test camera integration with a photo path
   * @param {string} photoPath - Path from camera photo
   * @returns {Promise<Object>} - Camera integration test result
   */
  static async testCameraIntegration(photoPath) {
    console.log(`📷 Testing camera integration with: ${photoPath}`);
    
    try {
      const startTime = Date.now();
      
      // Test using CameraBlurDetection utility
      const cameraResult = await CameraBlurDetection.analyzePhotoBlur(photoPath);
      
      // Test using ImageAnalysisService directly
      const serviceResult = await ImageAnalysisService.analyzeBlur(photoPath, 'path');
      
      const endTime = Date.now();
      
      const testResult = {
        success: true,
        photoPath,
        cameraUtilResult: cameraResult,
        serviceResult: serviceResult,
        resultsMatch: JSON.stringify(cameraResult) === JSON.stringify(serviceResult),
        analysisTime: endTime - startTime,
        timestamp: new Date().toISOString(),
      };
      
      console.log('✅ Camera integration test successful!');
      console.log(`📊 Results match: ${testResult.resultsMatch}`);
      console.log(`⏱️  Total time: ${testResult.analysisTime}ms`);
      
      if (cameraResult) {
        console.log(`🎯 Recommendation: ${cameraResult.isBlurry ? 'Retake photo' : 'Photo acceptable'}`);
      }
      
      return testResult;
    } catch (error) {
      console.error('❌ Camera integration test failed:', error);
      return {
        success: false,
        photoPath,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Performance test - run analysis multiple times on same image
   * @param {string} imagePath - Path to test image
   * @param {number} iterations - Number of iterations (default: 5)
   * @returns {Promise<Object>} - Performance test results
   */
  static async performanceTest(imagePath, iterations = 5) {
    console.log(`⚡ Running performance test: ${iterations} iterations on ${imagePath}`);
    
    const times = [];
    let lastResult = null;
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      const result = await ImageAnalysisService.analyzeBlur(imagePath, 'path');
      const endTime = Date.now();
      
      times.push(endTime - startTime);
      lastResult = result;
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    const performanceResult = {
      imagePath,
      iterations,
      times,
      averageTime: avgTime,
      minTime,
      maxTime,
      lastResult,
      timestamp: new Date().toISOString(),
    };
    
    console.log(`📊 Performance Results:`);
    console.log(`   Average: ${avgTime.toFixed(2)}ms`);
    console.log(`   Min: ${minTime}ms`);
    console.log(`   Max: ${maxTime}ms`);
    console.log(`   All times: [${times.join(', ')}]ms`);
    
    return performanceResult;
  }

  /**
   * Validate blur detection thresholds with known images
   * @param {Object[]} testCases - Array of {path, expectedBlurry, description}
   * @returns {Promise<Object>} - Threshold validation results
   */
  static async validateThresholds(testCases) {
    console.log(`🎯 Validating blur detection thresholds with ${testCases.length} test cases...`);
    
    const results = [];
    let correctPredictions = 0;
    
    for (const testCase of testCases) {
      try {
        const result = await ImageAnalysisService.analyzeBlur(testCase.path, 'path');
        
        if (result) {
          const isCorrect = result.isBlurry === testCase.expectedBlurry;
          if (isCorrect) correctPredictions++;
          
          results.push({
            ...testCase,
            actualBlurry: result.isBlurry,
            variance: result.variance,
            interpretation: result.interpretation,
            correct: isCorrect,
          });
          
          console.log(`${isCorrect ? '✅' : '❌'} ${testCase.description}: Expected ${testCase.expectedBlurry}, Got ${result.isBlurry} (${result.variance.toFixed(2)})`);
        } else {
          results.push({
            ...testCase,
            error: 'Analysis failed',
            correct: false,
          });
          console.log(`❌ ${testCase.description}: Analysis failed`);
        }
      } catch (error) {
        results.push({
          ...testCase,
          error: error.message,
          correct: false,
        });
        console.log(`❌ ${testCase.description}: Error - ${error.message}`);
      }
    }
    
    const accuracy = (correctPredictions / testCases.length) * 100;
    
    const validationResult = {
      totalCases: testCases.length,
      correctPredictions,
      accuracy,
      results,
      timestamp: new Date().toISOString(),
    };
    
    console.log(`📊 Threshold Validation Summary:`);
    console.log(`   Accuracy: ${accuracy.toFixed(1)}% (${correctPredictions}/${testCases.length})`);
    
    return validationResult;
  }
}

export default ImageAnalysisTestUtils;
